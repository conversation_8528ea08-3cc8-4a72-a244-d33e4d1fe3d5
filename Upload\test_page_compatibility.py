"""
测试页面兼容性修改
"""

import logging
import sys
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_page_compatibility.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_xpath_compatibility():
    """测试xpath兼容性修改"""
    try:
        logger.info("🧪 测试页面兼容性修改...")
        logger.info("=" * 50)
        
        # 导入修改后的模块
        from second_page_handler import SecondPageHandler
        
        # 创建一个模拟的SecondPageHandler实例
        class MockSecondPageHandler(SecondPageHandler):
            def __init__(self):
                # 不调用父类的__init__，避免需要driver
                self.reviewer_schemes = {
                    "DVP": {
                        "scheme_name": "数据管理员、部门相关方、系统专家、项目主管、车型品质主管、车型研发品质经理",
                        "scheme_xpath": "//input[@id='el-id-9858-115']",
                        "dropdown_xpath": "//div[contains(@class, 'el-select-dropdown')]//li[contains(text(), '数据管理员')]",
                        "roles": [
                            {"name": "部门相关方", "xpath": "//input[@id='el-id-9858-138']", "excel_role": "DVP_Signatory"},
                        ]
                    },
                    "FN": {
                        "scheme_name": "数据管理员、科长、相关方、项目主管",
                        "scheme_xpath": [
                            "//input[@id='el-id-9858-115']",
                            "//input[@id='el-id-8960-115']",
                            "//input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id-')]",
                            "//div[contains(@class, 'el-select__wrapper')]//input[@class='el-select__input']"
                        ],
                        "dropdown_xpath": "//div[contains(@class, 'el-select-dropdown')]//li[contains(text(), '数据管理员')]",
                        "roles": [
                            {"name": "数据管理员", "xpath": [
                                "//input[@id='el-id-9858-138']",
                                "//input[@id='el-id-8960-138']",
                                "//span[contains(text(), '数据管理员')]/ancestor::tr//input[@class='el-select__input']"
                            ], "excel_role": "Data_Manager"},
                        ]
                    },
                    "PPL": {
                        "scheme_name": "数据管理员、科长、相关方",
                        "scheme_xpath": [
                            "//input[@id='el-id-9858-115']",
                            "//input[@id='el-id-9125-115']",
                            "//input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id-')]",
                            "//div[contains(@class, 'el-select__wrapper')]//input[@class='el-select__input']"
                        ],
                        "dropdown_xpath": "//div[contains(@class, 'el-select-dropdown')]//li[contains(text(), '数据管理员')]",
                        "roles": [
                            {"name": "相关方", "xpath": [
                                "//input[@id='el-id-9858-138']",
                                "//input[@id='el-id-9125-138']",
                                "//span[contains(text(), '相关方')]/ancestor::tr//input[@class='el-select__input']"
                            ], "excel_role": "PPL_Signatory"},
                        ]
                    }
                }
        
        handler = MockSecondPageHandler()
        
        # 1. 测试scheme_xpath处理
        logger.info("1. 测试scheme_xpath处理:")
        for file_type in ["DVP", "FN", "PPL"]:
            scheme_config = handler.reviewer_schemes.get(file_type)
            if scheme_config:
                scheme_xpath = scheme_config['scheme_xpath']
                logger.info(f"   {file_type}:")
                
                if isinstance(scheme_xpath, str):
                    logger.info(f"     类型: 字符串")
                    logger.info(f"     值: {scheme_xpath}")
                elif isinstance(scheme_xpath, list):
                    logger.info(f"     类型: 列表")
                    logger.info(f"     长度: {len(scheme_xpath)}")
                    for i, xpath in enumerate(scheme_xpath):
                        logger.info(f"       {i+1}. {xpath}")
        
        # 2. 测试角色xpath处理
        logger.info("\n2. 测试角色xpath处理:")
        for file_type in ["FN", "PPL"]:
            scheme_config = handler.reviewer_schemes.get(file_type)
            if scheme_config:
                logger.info(f"   {file_type}:")
                for role in scheme_config['roles']:
                    role_name = role['name']
                    role_xpath = role['xpath']
                    logger.info(f"     角色: {role_name}")
                    
                    if isinstance(role_xpath, str):
                        logger.info(f"       类型: 字符串")
                        logger.info(f"       值: {role_xpath}")
                    elif isinstance(role_xpath, list):
                        logger.info(f"       类型: 列表")
                        logger.info(f"       长度: {len(role_xpath)}")
                        for i, xpath in enumerate(role_xpath):
                            logger.info(f"         {i+1}. {xpath}")
        
        # 3. 测试xpath兼容性
        logger.info("\n3. 测试xpath兼容性:")
        
        # 测试容器xpath
        container_xpaths = [
            "//*[@id='app']/div/div/div[2]/div/div[2]/div[2]",  # 新路径
            "//*[@id='app']/div/div/div[3]/div/div[2]/div[2]"   # 旧路径
        ]
        
        logger.info("   容器xpath:")
        for i, xpath in enumerate(container_xpaths, 1):
            logger.info(f"     {i}. {xpath}")
        
        # 测试模态框xpath
        modal_xpaths = [
            "//*[@id='app']/div/div/div[2]/div[4]/div/div/div",  # 新路径
            "//*[@id='app']/div/div/div[3]/div[4]/div/div/div"   # 旧路径
        ]
        
        logger.info("   模态框xpath:")
        for i, xpath in enumerate(modal_xpaths, 1):
            logger.info(f"     {i}. {xpath}")
        
        # 4. 测试ID前缀兼容性
        logger.info("\n4. 测试ID前缀兼容性:")
        
        id_patterns = {
            "FN": ["8960", "9858"],  # FN使用8960前缀
            "PPL": ["9125", "9858"]  # PPL使用9125前缀
        }
        
        for file_type, prefixes in id_patterns.items():
            logger.info(f"   {file_type}:")
            for prefix in prefixes:
                logger.info(f"     支持ID前缀: el-id-{prefix}-xxx")
        
        logger.info("\n✅ 页面兼容性测试完成!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_selector_strategies():
    """测试选择器策略"""
    logger.info("\n🧪 测试选择器策略...")
    
    # 测试通用选择器
    generic_selectors = [
        "//input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id-')]",
        "//div[contains(@class, 'el-select__wrapper')]//input[@class='el-select__input']",
        "//span[contains(text(), '数据管理员')]/ancestor::tr//input[@class='el-select__input']",
        "//input[@placeholder='请输入工号/姓名' and @class='el-select__input']"
    ]
    
    logger.info("通用选择器策略:")
    for i, selector in enumerate(generic_selectors, 1):
        logger.info(f"  {i}. {selector}")
    
    # 测试备用选择器
    fallback_selectors = [
        "//input[@role='combobox' and @class='el-select__input']",
        "//input[contains(@class, 'el-select__input') and @type='text']"
    ]
    
    logger.info("\n备用选择器:")
    for i, selector in enumerate(fallback_selectors, 1):
        logger.info(f"  {i}. {selector}")
    
    logger.info("✅ 选择器策略测试完成!")

def show_compatibility_summary():
    """显示兼容性总结"""
    logger.info("\n" + "=" * 60)
    logger.info("🎯 页面兼容性修改总结")
    logger.info("=" * 60)
    
    logger.info("\n✅ 已完成的修改:")
    modifications = [
        "更新容器xpath支持新旧两种路径 (div[2] 和 div[3])",
        "更新模态框xpath支持新旧两种路径",
        "FN页面支持ID前缀 8960 和 9858",
        "PPL页面支持ID前缀 9125 和 9858", 
        "scheme_xpath支持字符串和列表两种格式",
        "角色xpath支持字符串和列表两种格式",
        "增加更多通用选择器作为备用方案",
        "保持向后兼容性，支持旧版本页面"
    ]
    
    for i, mod in enumerate(modifications, 1):
        logger.info(f"  {i}. {mod}")
    
    logger.info("\n🎯 兼容性策略:")
    strategies = [
        "优先尝试新版本路径，失败后尝试旧版本",
        "支持多种ID前缀模式",
        "使用基于class的通用选择器",
        "基于文本内容的智能定位",
        "多重备用方案确保稳定性"
    ]
    
    for i, strategy in enumerate(strategies, 1):
        logger.info(f"  {i}. {strategy}")
    
    logger.info("\n💡 使用建议:")
    suggestions = [
        "程序会自动适配新旧版本页面",
        "无需手动配置，自动检测页面版本",
        "如遇到问题，查看日志了解使用的选择器",
        "建议在测试环境先验证兼容性"
    ]
    
    for i, suggestion in enumerate(suggestions, 1):
        logger.info(f"  {i}. {suggestion}")

if __name__ == "__main__":
    logger.info("🚀 开始页面兼容性测试...")
    
    # 运行测试
    test_result = test_xpath_compatibility()
    test_selector_strategies()
    show_compatibility_summary()
    
    if test_result:
        logger.info("\n🎉 页面兼容性测试通过！")
        logger.info("💡 程序已更新以支持FN和PPL页面的最新变化")
    else:
        logger.error("\n❌ 页面兼容性测试失败")
    
    logger.info("\n测试完成")
