2025-08-04 09:43:22,450 - INFO - 🚀 开始页面兼容性测试...
2025-08-04 09:43:22,450 - INFO - 🧪 测试页面兼容性修改...
2025-08-04 09:43:22,450 - INFO - ==================================================
2025-08-04 09:43:26,587 - INFO - 1. 测试scheme_xpath处理:
2025-08-04 09:43:26,587 - INFO -    DVP:
2025-08-04 09:43:26,587 - INFO -      类型: 字符串
2025-08-04 09:43:26,588 - INFO -      值: //input[@id='el-id-9858-115']
2025-08-04 09:43:26,588 - INFO -    FN:
2025-08-04 09:43:26,588 - INFO -      类型: 列表
2025-08-04 09:43:26,588 - INFO -      长度: 4
2025-08-04 09:43:26,588 - INFO -        1. //input[@id='el-id-9858-115']
2025-08-04 09:43:26,589 - INFO -        2. //input[@id='el-id-8960-115']
2025-08-04 09:43:26,589 - INFO -        3. //input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id-')]
2025-08-04 09:43:26,589 - INFO -        4. //div[contains(@class, 'el-select__wrapper')]//input[@class='el-select__input']
2025-08-04 09:43:26,589 - INFO -    PPL:
2025-08-04 09:43:26,590 - INFO -      类型: 列表
2025-08-04 09:43:26,590 - INFO -      长度: 4
2025-08-04 09:43:26,590 - INFO -        1. //input[@id='el-id-9858-115']
2025-08-04 09:43:26,590 - INFO -        2. //input[@id='el-id-9125-115']
2025-08-04 09:43:26,590 - INFO -        3. //input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id-')]
2025-08-04 09:43:26,590 - INFO -        4. //div[contains(@class, 'el-select__wrapper')]//input[@class='el-select__input']
2025-08-04 09:43:26,592 - INFO - 
2. 测试角色xpath处理:
2025-08-04 09:43:26,592 - INFO -    FN:
2025-08-04 09:43:26,592 - INFO -      角色: 数据管理员
2025-08-04 09:43:26,592 - INFO -        类型: 列表
2025-08-04 09:43:26,592 - INFO -        长度: 3
2025-08-04 09:43:26,592 - INFO -          1. //input[@id='el-id-9858-138']
2025-08-04 09:43:26,593 - INFO -          2. //input[@id='el-id-8960-138']
2025-08-04 09:43:26,593 - INFO -          3. //span[contains(text(), '数据管理员')]/ancestor::tr//input[@class='el-select__input']
2025-08-04 09:43:26,593 - INFO -    PPL:
2025-08-04 09:43:26,593 - INFO -      角色: 相关方
2025-08-04 09:43:26,593 - INFO -        类型: 列表
2025-08-04 09:43:26,593 - INFO -        长度: 3
2025-08-04 09:43:26,593 - INFO -          1. //input[@id='el-id-9858-138']
2025-08-04 09:43:26,594 - INFO -          2. //input[@id='el-id-9125-138']
2025-08-04 09:43:26,594 - INFO -          3. //span[contains(text(), '相关方')]/ancestor::tr//input[@class='el-select__input']
2025-08-04 09:43:26,594 - INFO - 
3. 测试xpath兼容性:
2025-08-04 09:43:26,594 - INFO -    容器xpath:
2025-08-04 09:43:26,594 - INFO -      1. //*[@id='app']/div/div/div[2]/div/div[2]/div[2]
2025-08-04 09:43:26,595 - INFO -      2. //*[@id='app']/div/div/div[3]/div/div[2]/div[2]
2025-08-04 09:43:26,595 - INFO -    模态框xpath:
2025-08-04 09:43:26,595 - INFO -      1. //*[@id='app']/div/div/div[2]/div[4]/div/div/div
2025-08-04 09:43:26,595 - INFO -      2. //*[@id='app']/div/div/div[3]/div[4]/div/div/div
2025-08-04 09:43:26,595 - INFO - 
4. 测试ID前缀兼容性:
2025-08-04 09:43:26,595 - INFO -    FN:
2025-08-04 09:43:26,596 - INFO -      支持ID前缀: el-id-8960-xxx
2025-08-04 09:43:26,596 - INFO -      支持ID前缀: el-id-9858-xxx
2025-08-04 09:43:26,596 - INFO -    PPL:
2025-08-04 09:43:26,596 - INFO -      支持ID前缀: el-id-9125-xxx
2025-08-04 09:43:26,596 - INFO -      支持ID前缀: el-id-9858-xxx
2025-08-04 09:43:26,597 - INFO - 
✅ 页面兼容性测试完成!
2025-08-04 09:43:26,597 - INFO - 
🧪 测试选择器策略...
2025-08-04 09:43:26,597 - INFO - 通用选择器策略:
2025-08-04 09:43:26,597 - INFO -   1. //input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id-')]
2025-08-04 09:43:26,597 - INFO -   2. //div[contains(@class, 'el-select__wrapper')]//input[@class='el-select__input']
2025-08-04 09:43:26,598 - INFO -   3. //span[contains(text(), '数据管理员')]/ancestor::tr//input[@class='el-select__input']
2025-08-04 09:43:26,598 - INFO -   4. //input[@placeholder='请输入工号/姓名' and @class='el-select__input']
2025-08-04 09:43:26,598 - INFO - 
备用选择器:
2025-08-04 09:43:26,598 - INFO -   1. //input[@role='combobox' and @class='el-select__input']
2025-08-04 09:43:26,599 - INFO -   2. //input[contains(@class, 'el-select__input') and @type='text']
2025-08-04 09:43:26,599 - INFO - ✅ 选择器策略测试完成!
2025-08-04 09:43:26,599 - INFO - 
============================================================
2025-08-04 09:43:26,599 - INFO - 🎯 页面兼容性修改总结
2025-08-04 09:43:26,599 - INFO - ============================================================
2025-08-04 09:43:26,599 - INFO - 
✅ 已完成的修改:
2025-08-04 09:43:26,599 - INFO -   1. 更新容器xpath支持新旧两种路径 (div[2] 和 div[3])
2025-08-04 09:43:26,600 - INFO -   2. 更新模态框xpath支持新旧两种路径
2025-08-04 09:43:26,600 - INFO -   3. FN页面支持ID前缀 8960 和 9858
2025-08-04 09:43:26,600 - INFO -   4. PPL页面支持ID前缀 9125 和 9858
2025-08-04 09:43:26,600 - INFO -   5. scheme_xpath支持字符串和列表两种格式
2025-08-04 09:43:26,603 - INFO -   6. 角色xpath支持字符串和列表两种格式
2025-08-04 09:43:26,606 - INFO -   7. 增加更多通用选择器作为备用方案
2025-08-04 09:43:26,606 - INFO -   8. 保持向后兼容性，支持旧版本页面
2025-08-04 09:43:26,607 - INFO - 
🎯 兼容性策略:
2025-08-04 09:43:26,607 - INFO -   1. 优先尝试新版本路径，失败后尝试旧版本
2025-08-04 09:43:26,607 - INFO -   2. 支持多种ID前缀模式
2025-08-04 09:43:26,608 - INFO -   3. 使用基于class的通用选择器
2025-08-04 09:43:26,608 - INFO -   4. 基于文本内容的智能定位
2025-08-04 09:43:26,608 - INFO -   5. 多重备用方案确保稳定性
2025-08-04 09:43:26,608 - INFO - 
💡 使用建议:
2025-08-04 09:43:26,608 - INFO -   1. 程序会自动适配新旧版本页面
2025-08-04 09:43:26,609 - INFO -   2. 无需手动配置，自动检测页面版本
2025-08-04 09:43:26,609 - INFO -   3. 如遇到问题，查看日志了解使用的选择器
2025-08-04 09:43:26,609 - INFO -   4. 建议在测试环境先验证兼容性
2025-08-04 09:43:26,609 - INFO - 
🎉 页面兼容性测试通过！
2025-08-04 09:43:26,609 - INFO - 💡 程序已更新以支持FN和PPL页面的最新变化
2025-08-04 09:43:26,610 - INFO - 
测试完成
