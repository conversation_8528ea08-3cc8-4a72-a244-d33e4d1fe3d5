2025-07-28 17:48:54,201 - INFO - 🚀 开始 second_page_handler 改进功能测试...
2025-07-28 17:48:54,201 - INFO - 
==================================================
2025-07-28 17:48:54,202 - INFO - 测试1: 输入框查找功能
2025-07-28 17:48:54,202 - INFO - ==================================================
2025-07-28 17:48:54,202 - INFO - 🧪 开始测试输入框查找功能...
2025-07-28 17:51:49,388 - ERROR - ❌ 输入框查找测试失败: SecondPageHandler.__init__() missing 2 required positional arguments: 'wait' and 'config'
2025-07-28 17:51:51,834 - INFO - 
==================================================
2025-07-28 17:51:51,835 - INFO - 测试2: 评审表头点击功能
2025-07-28 17:51:51,835 - INFO - ==================================================
2025-07-28 17:51:51,835 - INFO - 🧪 开始测试评审表头点击功能...
2025-07-28 17:51:56,273 - ERROR - ❌ 评审表头点击测试失败: SecondPageHandler.__init__() missing 2 required positional arguments: 'wait' and 'config'
2025-07-28 17:51:58,611 - INFO - 
==================================================
2025-07-28 17:51:58,632 - INFO - 测试3: 完整工作流程
2025-07-28 17:51:58,633 - INFO - ==================================================
2025-07-28 17:51:58,634 - INFO - 🧪 开始测试完整工作流程...
2025-07-28 17:52:03,026 - ERROR - ❌ 完整工作流程测试失败: SecondPageHandler.__init__() missing 2 required positional arguments: 'wait' and 'config'
2025-07-28 17:52:05,402 - INFO - 
==================================================
2025-07-28 17:52:05,402 - INFO - 测试结果汇总
2025-07-28 17:52:05,402 - INFO - ==================================================
2025-07-28 17:52:05,402 - INFO - 输入框查找功能: ❌ 失败
2025-07-28 17:52:05,403 - INFO - 评审表头点击功能: ❌ 失败
2025-07-28 17:52:05,403 - INFO - 完整工作流程: ❌ 失败
2025-07-28 17:52:05,403 - INFO - 
总计: 0/3 个测试通过
2025-07-28 17:52:05,403 - WARNING - ⚠️ 有 3 个测试失败
2025-07-28 17:59:04,257 - INFO - 🚀 开始 second_page_handler 改进功能测试...
2025-07-28 17:59:04,258 - INFO - 
==================================================
2025-07-28 17:59:04,258 - INFO - 测试1: 输入框查找功能
2025-07-28 17:59:04,258 - INFO - ==================================================
2025-07-28 17:59:04,258 - INFO - 🧪 开始测试输入框查找功能...
2025-07-28 17:59:08,691 - INFO - 🔍 测试查找角色: 部门相关方
2025-07-28 17:59:08,691 - INFO - 🔍 查找角色 部门相关方 的输入框...
2025-07-28 17:59:40,911 - INFO - ✅ 使用选择器 4 找到输入框: //span[contains(text(), '部门相关方')]/ancestor::tr//input[@class='el-select__input']
2025-07-28 17:59:41,919 - INFO - ✅ 成功找到 部门相关方 的输入框
2025-07-28 17:59:46,077 - INFO - ✅ 部门相关方 输入框交互测试成功
2025-07-28 17:59:46,079 - INFO - 🔍 测试查找角色: 系统专家
2025-07-28 17:59:46,080 - INFO - 🔍 查找角色 系统专家 的输入框...
2025-07-28 17:59:48,385 - INFO - ✅ 使用选择器 1 找到输入框: //input[@class='el-select__input']
2025-07-28 17:59:49,392 - INFO - ✅ 成功找到 系统专家 的输入框
2025-07-28 17:59:53,503 - INFO - ✅ 系统专家 输入框交互测试成功
2025-07-28 17:59:53,503 - INFO - 🔍 测试查找角色: 测试角色
2025-07-28 17:59:53,503 - INFO - 🔍 查找角色 测试角色 的输入框...
2025-07-28 17:59:55,798 - INFO - ✅ 使用选择器 1 找到输入框: //input[@role='combobox']
2025-07-28 17:59:56,804 - INFO - ✅ 成功找到 测试角色 的输入框
2025-07-28 18:00:00,945 - INFO - ✅ 测试角色 输入框交互测试成功
2025-07-28 18:00:03,399 - INFO - 
==================================================
2025-07-28 18:00:03,399 - INFO - 测试2: 评审表头点击功能
2025-07-28 18:00:03,400 - INFO - ==================================================
2025-07-28 18:00:03,400 - INFO - 🧪 开始测试评审表头点击功能...
2025-07-28 18:00:07,784 - INFO - 🖱️ 测试点击评审表头文字...
2025-07-28 18:00:07,784 - INFO - 🖱️ 点击评审表头文字让人员填写生效...
2025-07-28 18:00:10,894 - INFO - ✅ 成功点击表头文字: 评审角色
2025-07-28 18:00:10,894 - INFO - ✅ 评审表头点击测试成功
2025-07-28 18:00:13,349 - INFO - 
==================================================
2025-07-28 18:00:13,349 - INFO - 测试3: 完整工作流程
2025-07-28 18:00:13,349 - INFO - ==================================================
2025-07-28 18:00:13,350 - INFO - 🧪 开始测试完整工作流程...
2025-07-28 18:00:17,729 - INFO - 🔄 测试填写 2 个人员到角色: 测试角色
2025-07-28 18:00:17,729 - INFO - 👤 为角色 测试角色 填写 2 个人员...
2025-07-28 18:00:17,729 - INFO - 🔄 处理第 1/2 个人员...
2025-07-28 18:00:17,730 - INFO - 🔍 查找角色 测试角色 的输入框...
2025-07-28 18:01:20,211 - INFO - ✅ 使用选择器 7 找到输入框: //input[@role='combobox' and @class='el-select__input']
2025-07-28 18:01:21,218 - INFO - ✅ 有效的搜索文本: <EMAIL>
2025-07-28 18:01:21,218 - INFO - 🔍 输入人员信息: <EMAIL>
2025-07-28 18:01:24,290 - INFO - ✅ 方法1成功激活输入框
2025-07-28 18:01:28,366 - INFO - ✅ 成功输入搜索文本: <EMAIL>
2025-07-28 18:01:28,366 - INFO - 🔍 等待并选择搜索结果: <EMAIL>
2025-07-28 18:02:01,176 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000211522D3FE0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/98fe67e6a89c41e2d1f60d966f692cb2
2025-07-28 18:02:05,181 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000211522D3EC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/98fe67e6a89c41e2d1f60d966f692cb2
2025-07-28 18:02:09,188 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021152314140>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/98fe67e6a89c41e2d1f60d966f692cb2
