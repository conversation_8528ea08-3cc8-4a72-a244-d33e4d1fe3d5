"""
分析FN和PPL页面变化，评估是否需要修改上传程序
"""

import logging
import re
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analyze_page_changes.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def analyze_html_structure():
    """分析HTML结构变化"""
    try:
        logger.info("🔍 分析FN和PPL页面结构变化...")
        logger.info("=" * 60)
        
        # 读取新的HTML文件
        fn_html = Path("../fn.html").read_text(encoding='utf-8')
        ppl_html = Path("../ppl.html").read_text(encoding='utf-8')
        xpath_info = Path("../xpath.txt").read_text(encoding='utf-8').strip()
        
        logger.info(f"📄 FN HTML文件大小: {len(fn_html)} 字符")
        logger.info(f"📄 PPL HTML文件大小: {len(ppl_html)} 字符")
        logger.info(f"📍 新的容器xpath: {xpath_info}")
        
        # 分析关键结构变化
        analyze_key_changes(fn_html, ppl_html, xpath_info)
        
        # 分析元素ID变化
        analyze_element_ids(fn_html, ppl_html)
        
        # 分析表单结构
        analyze_form_structure(fn_html, ppl_html)
        
        # 分析上传组件
        analyze_upload_components(fn_html, ppl_html)
        
        # 评估兼容性
        assess_compatibility()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 分析失败: {str(e)}")
        return False

def analyze_key_changes(fn_html, ppl_html, xpath_info):
    """分析关键结构变化"""
    logger.info("\n🔍 关键结构变化分析:")
    
    # 1. 容器xpath变化
    logger.info("1. 容器xpath变化:")
    logger.info(f"   新xpath: {xpath_info}")
    logger.info("   影响: 需要更新程序中的容器定位逻辑")
    
    # 2. 页面布局结构
    logger.info("\n2. 页面布局结构:")
    
    # 检查侧边栏结构
    if 'class="sidebar"' in fn_html and 'class="sidebar"' in ppl_html:
        logger.info("   ✅ 侧边栏结构保持一致")
    
    # 检查标签页结构
    if 'el-tabs' in fn_html and 'el-tabs' in ppl_html:
        logger.info("   ✅ 标签页结构保持一致")
    
    # 检查折叠面板结构
    if 'el-collapse' in fn_html and 'el-collapse' in ppl_html:
        logger.info("   ✅ 折叠面板结构保持一致")

def analyze_element_ids(fn_html, ppl_html):
    """分析元素ID变化"""
    logger.info("\n🔍 元素ID变化分析:")
    
    # 提取FN中的ID
    fn_ids = set(re.findall(r'el-id-(\d+)-(\d+)', fn_html))
    ppl_ids = set(re.findall(r'el-id-(\d+)-(\d+)', ppl_html))
    
    logger.info(f"   FN页面ID前缀: {set([id_pair[0] for id_pair in fn_ids])}")
    logger.info(f"   PPL页面ID前缀: {set([id_pair[0] for id_pair in ppl_ids])}")
    
    # 检查是否有ID模式变化
    fn_prefixes = set([id_pair[0] for id_pair in fn_ids])
    ppl_prefixes = set([id_pair[0] for id_pair in ppl_ids])
    
    if '8960' in fn_prefixes or '8960' in ppl_prefixes:
        logger.info("   ⚠️ 发现旧的ID前缀8960，可能需要更新")
    
    if '9125' in fn_prefixes or '9125' in ppl_prefixes:
        logger.info("   ✅ 发现新的ID前缀9125")
    
    # 分析ID变化影响
    logger.info("\n   ID变化影响评估:")
    logger.info("   - 程序中使用动态ID选择器，影响较小")
    logger.info("   - 建议增加更多通用选择器作为备用")

def analyze_form_structure(fn_html, ppl_html):
    """分析表单结构"""
    logger.info("\n🔍 表单结构分析:")
    
    # 检查关键表单区域
    form_sections = [
        ("编制信息", "编制人"),
        ("文件上传", "源文件"),
        ("文档信息", "文档名称"),
        ("评审信息", "评审类型")
    ]
    
    for section_name, field_name in form_sections:
        fn_has_section = section_name in fn_html
        ppl_has_section = section_name in ppl_html
        fn_has_field = field_name in fn_html
        ppl_has_field = field_name in ppl_html
        
        if fn_has_section and ppl_has_section:
            logger.info(f"   ✅ {section_name}区域: 两个页面都存在")
            if fn_has_field and ppl_has_field:
                logger.info(f"      ✅ {field_name}字段: 两个页面都存在")
            else:
                logger.info(f"      ⚠️ {field_name}字段: 存在差异")
        else:
            logger.info(f"   ⚠️ {section_name}区域: 存在差异")

def analyze_upload_components(fn_html, ppl_html):
    """分析上传组件"""
    logger.info("\n🔍 上传组件分析:")
    
    # 检查上传相关的关键元素
    upload_elements = [
        ('el-upload', '上传组件'),
        ('el-upload__input', '文件输入框'),
        ('el-upload--text', '文本上传模式'),
        ('type="file"', '文件类型输入'),
        ('附件-非受控', '附件上传区域')
    ]
    
    for element, description in upload_elements:
        fn_count = fn_html.count(element)
        ppl_count = ppl_html.count(element)
        
        logger.info(f"   {description}:")
        logger.info(f"      FN: {fn_count}个, PPL: {ppl_count}个")
        
        if fn_count > 0 and ppl_count > 0:
            logger.info(f"      ✅ 两个页面都存在")
        elif fn_count != ppl_count:
            logger.info(f"      ⚠️ 数量不一致，可能需要调整")

def assess_compatibility():
    """评估兼容性"""
    logger.info("\n🎯 兼容性评估:")
    logger.info("=" * 40)
    
    compatibility_items = [
        {
            "item": "页面基本结构",
            "status": "✅ 兼容",
            "reason": "Element UI组件结构保持一致",
            "action": "无需修改"
        },
        {
            "item": "容器xpath",
            "status": "⚠️ 需要更新",
            "reason": "最外层容器xpath发生变化",
            "action": "更新容器定位逻辑"
        },
        {
            "item": "元素ID",
            "status": "⚠️ 可能需要调整",
            "reason": "ID前缀可能从8960变为9125",
            "action": "增加更多通用选择器"
        },
        {
            "item": "表单字段",
            "status": "✅ 兼容",
            "reason": "关键表单字段结构保持一致",
            "action": "无需修改"
        },
        {
            "item": "上传组件",
            "status": "✅ 兼容",
            "reason": "el-upload组件结构未变化",
            "action": "无需修改"
        },
        {
            "item": "选择器策略",
            "status": "✅ 基本兼容",
            "reason": "程序使用多重选择器策略",
            "action": "建议增加更多备用选择器"
        }
    ]
    
    for item in compatibility_items:
        logger.info(f"{item['status']} {item['item']}")
        logger.info(f"   原因: {item['reason']}")
        logger.info(f"   建议: {item['action']}")
        logger.info("")

def generate_recommendations():
    """生成修改建议"""
    logger.info("💡 修改建议:")
    logger.info("=" * 30)
    
    recommendations = [
        {
            "priority": "高",
            "item": "更新容器xpath",
            "details": [
                "将 //*[@id='app']/div/div/div[3] 更新为 //*[@id='app']/div/div/div[2]",
                "更新所有基于容器路径的选择器",
                "测试新的容器定位是否正确"
            ]
        },
        {
            "priority": "中",
            "item": "增强选择器兼容性",
            "details": [
                "添加更多基于class的通用选择器",
                "减少对特定ID的依赖",
                "增加基于文本内容的定位方法"
            ]
        },
        {
            "priority": "低",
            "item": "优化错误处理",
            "details": [
                "增加页面结构变化的检测",
                "提供更详细的错误信息",
                "添加自动适配机制"
            ]
        }
    ]
    
    for rec in recommendations:
        logger.info(f"🔥 {rec['priority']}优先级: {rec['item']}")
        for detail in rec['details']:
            logger.info(f"   - {detail}")
        logger.info("")

if __name__ == "__main__":
    logger.info("🚀 开始分析FN和PPL页面变化...")
    
    # 运行分析
    if analyze_html_structure():
        generate_recommendations()
        logger.info("\n🎉 分析完成！")
        logger.info("💡 总结: 页面结构基本兼容，主要需要更新容器xpath")
    else:
        logger.error("\n❌ 分析失败")
    
    logger.info("\n分析完成")
