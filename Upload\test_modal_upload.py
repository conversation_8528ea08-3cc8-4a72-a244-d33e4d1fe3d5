"""
测试模态框PDF上传功能
"""

import logging
import time
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_modal_upload.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_modal_upload_functionality():
    """测试模态框上传功能"""
    try:
        logger.info("🧪 开始测试模态框PDF上传功能...")
        
        # 检查PDF文件是否存在
        documents_folder = Path("Final_Approval_Documents")
        if not documents_folder.exists():
            logger.error("❌ Final_Approval_Documents文件夹不存在")
            return False
        
        pdf_files = list(documents_folder.glob("*.pdf"))
        if not pdf_files:
            logger.error("❌ Final_Approval_Documents文件夹中没有PDF文件")
            return False
        
        logger.info(f"✅ 找到 {len(pdf_files)} 个PDF文件:")
        for pdf_file in pdf_files:
            logger.info(f"  - {pdf_file.name}")
        
        # 测试文件路径获取功能
        from second_page_handler import SecondPageHandler
        
        # 创建一个模拟的SecondPageHandler实例（不需要真实的driver）
        class MockSecondPageHandler(SecondPageHandler):
            def __init__(self):
                # 不调用父类的__init__，避免需要driver
                pass
            
            def _get_pdf_file_path(self, filename=None):
                """重写方法以便测试"""
                return super()._get_pdf_file_path(filename)
        
        handler = MockSecondPageHandler()
        
        # 测试不同的文件名匹配
        test_cases = [
            None,  # 测试无文件名情况
            "HYHB_FN_A19-000011-接口定义通知单.docx",  # 测试有文件名情况
            "HC2_PPL_A19-000001-匹配计划.xlsx",  # 测试另一个文件名
        ]
        
        for test_filename in test_cases:
            logger.info(f"🔍 测试文件名: {test_filename}")
            pdf_path = handler._get_pdf_file_path(test_filename)
            if pdf_path:
                logger.info(f"✅ 获取到PDF路径: {pdf_path}")
            else:
                logger.warning(f"⚠️ 未获取到PDF路径")
        
        logger.info("✅ 模态框PDF上传功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_modal_selectors():
    """测试模态框选择器的有效性"""
    try:
        logger.info("🧪 测试模态框选择器...")
        
        # 测试选择器的语法正确性
        selectors_to_test = {
            "模态框": "//*[@id='app']/div/div/div[3]/div[4]/div/div/div",
            "重新上传按钮": [
                "//button[contains(@class, 'el-button--primary') and contains(@class, 'is-link') and .//span[text()='重新上传']]",
                "//div[@class='el-upload el-upload--text']//button[contains(@class, 'el-button--primary')]",
                "//button[.//span[text()='重新上传']]",
            ],
            "文件输入框": [
                "//div[@class='el-upload el-upload--text']//input[@class='el-upload__input']",
                "//input[@class='el-upload__input'][@type='file']",
                "//input[@type='file'][@name='file']",
            ],
            "继续提交按钮": [
                "//footer[@class='el-dialog__footer']//button[contains(@class, 'el-button--primary') and .//span[text()='继续提交']]",
                "//div[contains(@style, 'text-align: center')]//button[contains(@class, 'el-button--primary') and .//span[text()='继续提交']]",
                "//button[.//span[text()='继续提交']]",
            ]
        }
        
        for selector_name, selectors in selectors_to_test.items():
            logger.info(f"🔍 测试 {selector_name} 选择器...")
            if isinstance(selectors, str):
                selectors = [selectors]
            
            for i, selector in enumerate(selectors):
                try:
                    # 简单的语法检查 - 确保XPath格式正确
                    if selector.startswith("//") or selector.startswith("/"):
                        logger.info(f"  ✅ 选择器 {i+1} 语法正确: {selector[:50]}...")
                    else:
                        logger.warning(f"  ⚠️ 选择器 {i+1} 可能有问题: {selector[:50]}...")
                except Exception as e:
                    logger.error(f"  ❌ 选择器 {i+1} 有错误: {str(e)}")
        
        logger.info("✅ 选择器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 选择器测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始测试模态框PDF上传功能...")
    
    # 运行测试
    test1_result = test_modal_upload_functionality()
    test2_result = test_modal_selectors()
    
    if test1_result and test2_result:
        logger.info("🎉 所有测试通过！")
    else:
        logger.error("❌ 部分测试失败")
    
    logger.info("测试完成")
