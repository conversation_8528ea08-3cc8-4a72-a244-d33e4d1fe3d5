#!/usr/bin/env python3
"""
测试脚本 - 验证PPL和FN评审人方案选择修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Upload.second_page_handler import SecondPageHandler
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_reviewer_scheme_config():
    """测试评审人方案配置"""
    logger.info("🧪 测试评审人方案配置...")
    
    # 创建一个模拟的配置
    config = {}
    handler = SecondPageHandler(None, None, config)
    
    # 测试FN配置
    fn_config = handler.reviewer_schemes.get("FN")
    if fn_config:
        logger.info(f"✅ FN配置存在")
        logger.info(f"   方案名称: {fn_config['scheme_name']}")
        logger.info(f"   输入框xpath数量: {len(fn_config['scheme_xpath'])}")
        logger.info(f"   角色数量: {len(fn_config['roles'])}")
        
        # 检查是否包含正确的ID
        scheme_xpaths = fn_config['scheme_xpath']
        has_correct_id = any("el-id-8960-115" in xpath for xpath in scheme_xpaths)
        logger.info(f"   包含正确的FN ID (el-id-8960-115): {has_correct_id}")
    else:
        logger.error("❌ FN配置不存在")
    
    # 测试PPL配置
    ppl_config = handler.reviewer_schemes.get("PPL")
    if ppl_config:
        logger.info(f"✅ PPL配置存在")
        logger.info(f"   方案名称: {ppl_config['scheme_name']}")
        logger.info(f"   输入框xpath数量: {len(ppl_config['scheme_xpath'])}")
        logger.info(f"   角色数量: {len(ppl_config['roles'])}")
        
        # 检查是否包含正确的ID
        scheme_xpaths = ppl_config['scheme_xpath']
        has_correct_id = any("el-id-9125-115" in xpath for xpath in scheme_xpaths)
        logger.info(f"   包含正确的PPL ID (el-id-9125-115): {has_correct_id}")
    else:
        logger.error("❌ PPL配置不存在")

def test_precise_locator():
    """测试精确定位方法"""
    logger.info("🧪 测试精确定位方法...")
    
    config = {}
    handler = SecondPageHandler(None, None, config)
    
    # 测试不同文件类型的ID映射
    test_cases = [
        ("FN", "el-id-8960-115"),
        ("PPL", "el-id-9125-115"),
        ("DVP", "el-id-9858-115")
    ]
    
    for file_type, expected_id in test_cases:
        logger.info(f"   测试 {file_type} -> 期望ID: {expected_id}")
        # 这里我们只能测试逻辑，因为没有真实的driver
        # 但可以验证配置是否正确
        scheme_config = handler.reviewer_schemes.get(file_type)
        if scheme_config:
            scheme_xpaths = scheme_config.get('scheme_xpath', [])
            if isinstance(scheme_xpaths, str):
                scheme_xpaths = [scheme_xpaths]
            
            has_expected_id = any(expected_id in xpath for xpath in scheme_xpaths)
            logger.info(f"   ✅ {file_type} 配置包含期望ID: {has_expected_id}")
        else:
            logger.error(f"   ❌ {file_type} 配置不存在")

def test_scheme_matching():
    """测试方案匹配逻辑"""
    logger.info("🧪 测试方案匹配逻辑...")
    
    config = {}
    handler = SecondPageHandler(None, None, config)
    
    # 测试方案匹配
    test_cases = [
        ("数据管理员、科长、相关方", "数据管理员、科长、相关方", True),
        ("数据管理员、科长、相关方", "数据管理员、科长、相关方、项目主管", False),
        ("数据管理员、科长、相关方", "数据管理员、科长", False),
        ("数据管理员、科长、相关方", "数据管理员、科长、相关方（PPL）", True),  # 应该匹配70%以上
    ]
    
    for target_scheme, item_text, expected in test_cases:
        result = handler._scheme_matches(item_text, target_scheme)
        status = "✅" if result == expected else "❌"
        logger.info(f"   {status} '{item_text}' 匹配 '{target_scheme}': {result} (期望: {expected})")

def main():
    """主测试函数"""
    logger.info("🚀 开始测试评审人方案选择修复...")
    
    try:
        test_reviewer_scheme_config()
        print()
        test_precise_locator()
        print()
        test_scheme_matching()
        
        logger.info("✅ 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
