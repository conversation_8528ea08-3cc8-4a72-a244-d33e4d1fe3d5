2025-07-29 12:47:24,065 - INFO - 🚀 开始测试模态框PDF上传功能...
2025-07-29 12:47:24,065 - INFO - 🧪 开始测试模态框PDF上传功能...
2025-07-29 12:47:24,066 - INFO - ✅ 找到 2 个PDF文件:
2025-07-29 12:47:24,066 - INFO -   - HC2_PPL_A19-000001-匹配计划.pdf
2025-07-29 12:47:24,066 - INFO -   - HYHB_FN_A19-000011-接口定义通知单.pdf
2025-07-29 12:47:27,104 - INFO - 🔍 测试文件名: None
2025-07-29 12:47:27,104 - INFO - 🔍 查找要上传的PDF文件...
2025-07-29 12:47:27,105 - INFO - ✅ 使用最新的PDF文件: HC2_PPL_A19-000001-匹配计划.pdf
2025-07-29 12:47:27,105 - INFO - ✅ 获取到PDF路径: D:\work\Apply_Fillin_Upload_zhengli\Upload\Final_Approval_Documents\HC2_PPL_A19-000001-匹配计划.pdf
2025-07-29 12:47:27,106 - INFO - 🔍 测试文件名: HYHB_FN_A19-000011-接口定义通知单.docx
2025-07-29 12:47:27,106 - INFO - 🔍 查找要上传的PDF文件...
2025-07-29 12:47:27,106 - INFO - 🔍 尝试匹配文件名: HYHB_FN_A19-000011-接口定义通知单.docx
2025-07-29 12:47:27,107 - INFO - ✅ 找到匹配的PDF文件: HYHB_FN_A19-000011-接口定义通知单.pdf
2025-07-29 12:47:27,107 - INFO - ✅ 获取到PDF路径: D:\work\Apply_Fillin_Upload_zhengli\Upload\Final_Approval_Documents\HYHB_FN_A19-000011-接口定义通知单.pdf
2025-07-29 12:47:27,107 - INFO - 🔍 测试文件名: HC2_PPL_A19-000001-匹配计划.xlsx
2025-07-29 12:47:27,107 - INFO - 🔍 查找要上传的PDF文件...
2025-07-29 12:47:27,108 - INFO - 🔍 尝试匹配文件名: HC2_PPL_A19-000001-匹配计划.xlsx
2025-07-29 12:47:27,108 - INFO - ✅ 找到匹配的PDF文件: HC2_PPL_A19-000001-匹配计划.pdf
2025-07-29 12:47:27,108 - INFO - ✅ 获取到PDF路径: D:\work\Apply_Fillin_Upload_zhengli\Upload\Final_Approval_Documents\HC2_PPL_A19-000001-匹配计划.pdf
2025-07-29 12:47:27,108 - INFO - ✅ 模态框PDF上传功能测试完成
2025-07-29 12:47:27,108 - INFO - 🧪 测试模态框选择器...
2025-07-29 12:47:27,108 - INFO - 🔍 测试 模态框 选择器...
2025-07-29 12:47:27,108 - INFO -   ✅ 选择器 1 语法正确: //*[@id='app']/div/div/div[3]/div[4]/div/div/div...
2025-07-29 12:47:27,108 - INFO - 🔍 测试 重新上传按钮 选择器...
2025-07-29 12:47:27,109 - INFO -   ✅ 选择器 1 语法正确: //button[contains(@class, 'el-button--primary') an...
2025-07-29 12:47:27,109 - INFO -   ✅ 选择器 2 语法正确: //div[@class='el-upload el-upload--text']//button[...
2025-07-29 12:47:27,109 - INFO -   ✅ 选择器 3 语法正确: //button[.//span[text()='重新上传']]...
2025-07-29 12:47:27,109 - INFO - 🔍 测试 文件输入框 选择器...
2025-07-29 12:47:27,110 - INFO -   ✅ 选择器 1 语法正确: //div[@class='el-upload el-upload--text']//input[@...
2025-07-29 12:47:27,110 - INFO -   ✅ 选择器 2 语法正确: //input[@class='el-upload__input'][@type='file']...
2025-07-29 12:47:27,110 - INFO -   ✅ 选择器 3 语法正确: //input[@type='file'][@name='file']...
2025-07-29 12:47:27,110 - INFO - 🔍 测试 继续提交按钮 选择器...
2025-07-29 12:47:27,110 - INFO -   ✅ 选择器 1 语法正确: //footer[@class='el-dialog__footer']//button[conta...
2025-07-29 12:47:27,110 - INFO -   ✅ 选择器 2 语法正确: //div[contains(@style, 'text-align: center')]//but...
2025-07-29 12:47:27,111 - INFO -   ✅ 选择器 3 语法正确: //button[.//span[text()='继续提交']]...
2025-07-29 12:47:27,112 - INFO - ✅ 选择器测试完成
2025-07-29 12:47:27,112 - INFO - 🎉 所有测试通过！
2025-07-29 12:47:27,112 - INFO - 测试完成
