"""
第二个页面处理模块
处理评审人方案选择、评审信息填写等功能
"""

from datetime import datetime, timedelta
import time
import logging
import re
import os
import pandas as pd
from pathlib import Path
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException

logger = logging.getLogger(__name__)

class SecondPageHandler:
    """第二个页面处理器"""
    
    def __init__(self, driver, wait, config):
        self.driver = driver
        self.wait = wait
        self.config = config
        
        # 文件类型对应的评审人方案配置 - 基于新的HTML结构
        self.reviewer_schemes = {
            "DVP": {
                "scheme_name": "数据管理员、部门相关方、系统专家、项目主管、车型品质主管、车型研发品质经理",
                "scheme_xpath": "//input[@id='el-id-9858-115']",  # 评审人方案输入框
                "dropdown_xpath": "//div[contains(@class, 'el-select-dropdown')]//li[contains(text(), '数据管理员')]",
                "roles": [
                    {"name": "部门相关方", "xpath": "//input[@id='el-id-9858-138']", "excel_role": "DVP_Signatory"},
                    {"name": "系统专家", "xpath": "//tbody//tr[2]//input[contains(@class, 'el-select__input')]", "excel_role": "Experts"},
                    {"name": "项目主管", "xpath": "//tbody//tr[3]//input[contains(@class, 'el-select__input')]", "excel_role": "ChassisProjectManager"},
                    {"name": "车型品质主管", "xpath": "//tbody//tr[4]//input[contains(@class, 'el-select__input')]", "excel_role": "V_QualityManager"},
                    {"name": "车型研发品质经理", "xpath": "//tbody//tr[5]//input[contains(@class, 'el-select__input')]", "excel_role": "RD_QualityManager"},
                    {"name": "数据管理员", "xpath": "//tbody//tr[6]//input[contains(@class, 'el-select__input')]", "excel_role": "Data_Manager"}
                ]
            },
            "FN": {
                "scheme_name": "数据管理员、科长、相关方、项目主管",
                # 使用更通用的选择器，支持不同的ID前缀
                "scheme_xpath": [
                    "//input[@id='el-id-9858-115']",  # 旧版本ID
                    "//input[@id='el-id-8960-115']",  # 新版本ID (FN)
                    "//input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id-')]",  # 通用ID模式
                    "//div[contains(@class, 'el-select__wrapper')]//input[@class='el-select__input']"  # 基于class的通用选择器
                ],
                "dropdown_xpath": "//div[contains(@class, 'el-select-dropdown')]//li[contains(text(), '数据管理员')]",
                "roles": [
                    {"name": "数据管理员", "xpath": [
                        "//input[@id='el-id-9858-138']",  # 旧版本
                        "//input[@id='el-id-8960-138']",  # 新版本
                        "//span[contains(text(), '数据管理员')]/ancestor::tr//input[@class='el-select__input']"  # 通用
                    ], "excel_role": "Data_Manager"},
                    {"name": "科长", "xpath": "//tbody//tr[2]//input[contains(@class, 'el-select__input')]", "excel_role": "SectionHead"},
                    {"name": "相关方", "xpath": "//tbody//tr[3]//input[contains(@class, 'el-select__input')]", "excel_role": "dynamic"},  # 动态确定
                    {"name": "项目主管", "xpath": "//tbody//tr[4]//input[contains(@class, 'el-select__input')]", "excel_role": "ChassisProjectManager"}
                ]
            },
            "PPL": {
                "scheme_name": "数据管理员、科长、相关方",
                # 使用更通用的选择器，支持不同的ID前缀
                "scheme_xpath": [
                    "//input[@id='el-id-9858-115']",  # 旧版本ID
                    "//input[@id='el-id-9125-115']",  # 新版本ID (PPL)
                    "//input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id-')]",  # 通用ID模式
                    "//div[contains(@class, 'el-select__wrapper')]//input[@class='el-select__input']"  # 基于class的通用选择器
                ],
                "dropdown_xpath": "//div[contains(@class, 'el-select-dropdown')]//li[contains(text(), '数据管理员')]",
                "roles": [
                    {"name": "相关方", "xpath": [
                        "//input[@id='el-id-9858-138']",  # 旧版本
                        "//input[@id='el-id-9125-138']",  # 新版本
                        "//span[contains(text(), '相关方')]/ancestor::tr//input[@class='el-select__input']"  # 通用
                    ], "excel_role": "PPL_Signatory"},
                    {"name": "数据管理员", "xpath": "//tbody//tr[2]//input[contains(@class, 'el-select__input')]", "excel_role": "Data_Manager"},
                    {"name": "科长", "xpath": "//tbody//tr[3]//input[contains(@class, 'el-select__input')]", "excel_role": "SectionHead"}
                ]
            }
        }
        
        # FN文件名模式匹配 - 优化DiSus系列匹配
        self.fn_file_patterns = {
            # DiSus系列 - 优先匹配，模式更宽松
            "FN_DiSus_A": (r"DiSus[_-]?A", "接口定义通知单"),
            "FN_DiSus_C": (r"DiSus[_-]?C", "接口定义通知单"), 
            "FN_DiSus_P": (r"DiSus[_-]?P", "接口定义通知单"),
            "FN_DiSus_X": (r"DiSus[_-]?X", "接口定义通知单"),
            "FN_DiSus_M": (r"DiSus[_-]?M", "接口定义通知单"),
            # 其他系统
            "FN_VCU": (r"VCU", "接口定义通知单"),
            "FN_IPB": (r"IPB", "接口定义通知单"),
            "FN_ESP_BWA": (r"ESP", "接口定义通知单"),
            "FN_EPS": (r"EPS(?!A)", "接口定义通知单"),  # EPS但不是EPSA
            "FN_EPSA": (r"EPSA", "接口定义通知单"),
            "FN_EPB": (r"EPB", "接口定义通知单"),
            "FN_域控": (r"域.*控", "接口定义通知单"),
            "FN_IMU": (r"气囊|IMU", "接口定义通知单")  # 默认放最后
        }
    
    def process_second_page(self, doc_id, filename=None):
        """处理第二个页面的主要流程（通过文档编号）"""
        try:
            logger.info(f"📋 开始处理第二个页面: {doc_id}")
            
            # 从文档编号识别文件类型
            file_type = self._identify_file_type_from_doc_id(doc_id)
            if not file_type:
                logger.error(f"❌ 无法从文档编号 {doc_id} 识别文件类型")
                return False
            
            # 调用通用处理方法，传递文件名
            return self.handle_second_page(file_type, filename)
            
        except Exception as e:
            logger.error(f"❌ 第二页处理失败: {str(e)}")
            return False
    
    def handle_second_page(self, file_type, filename=None):
        """处理第二个页面 - 主控制器调用的方法"""
        try:
            logger.info(f"📋 开始处理第二个页面，文件类型: {file_type}")
            if filename:
                logger.info(f"📄 处理文件: {filename}")
            else:
                logger.warning("⚠️ 未提供文件名，FN类型将使用默认角色")
            
            # 1. 检查并切换到正确的标签页
            if not self._switch_to_second_page_tab():
                return False
            time.sleep(3)  # 等待标签页切换完成
            
            # 2. 等待页面加载
            if not self._wait_for_page_load():
                return False
            time.sleep(2)  # 等待页面完全加载
            
            # 3. 滚动到页面下方加载评审人信息区域
            if not self._scroll_to_reviewer_section():
                return False
            time.sleep(2)  # 等待滚动和区域加载
            
            # 4. 读取Excel数据
            excel_data = self._load_excel_data()
            if excel_data is None or excel_data.empty:
                logger.error("❌ 读取Excel数据失败")
                return False
            time.sleep(1)  # 数据处理间隔

            # 5. 先填写截止时间 (新的处理顺序)
            if not self._fill_deadline():
                return False
            time.sleep(2)  # 等待时间填写完成

            # 6. 选择对应的评审人方案
            if not self._select_reviewer_scheme(file_type):
                return False
            time.sleep(3)  # 等待方案选择完成和页面更新

            # 7. 填写评审人
            if not self._fill_reviewers(file_type, excel_data, filename):
                return False
            time.sleep(2)  # 等待填写完成

            # 8. 点击提交按钮
            if not self._submit_form():
                return False
            time.sleep(3)  # 等待提交完成

            # 9. 处理提交后的模态框（PDF上传）
            if not self._handle_post_submit_modal(filename):
                return False

            logger.info(f"✅ 第二页处理完成，文件类型: {file_type}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 第二页处理失败: {str(e)}")
            return False
    
    def _wait_for_page_load(self):
        """等待第二个页面加载完成 - 增强版多种检测方法"""
        try:
            logger.info("⏳ 等待第二个页面加载...")

            # 方法1: 等待最外层容器元素出现 (支持新旧两种路径)
            try:
                # 尝试新的容器路径
                try:
                    main_container = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//*[@id='app']/div/div/div[2]/div/div[2]/div[2]"))
                    )
                    logger.info("✅ 方法1: 找到新版本容器 (div[2])")
                except TimeoutException:
                    # 如果新路径失败，尝试旧路径
                    main_container = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//*[@id='app']/div/div/div[3]/div/div[2]/div[2]"))
                    )
                    logger.info("✅ 方法1: 找到旧版本容器 (div[3])")
                time.sleep(2)
                return True
            except TimeoutException:
                logger.warning("⚠️ 方法1: 未找到最外层容器，尝试其他方法")

            # 方法2: 等待评审人信息区域出现 (新的ID)
            try:
                reviewer_section = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, "el-collapse-content-97"))
                )
                logger.info("✅ 方法2: 评审人信息区域已找到")
                time.sleep(2)
                return True
            except TimeoutException:
                logger.warning("⚠️ 方法2: 未找到评审人信息区域，尝试其他方法")

            # 方法3: 等待评审信息区域出现
            try:
                review_info_section = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, "el-collapse-content-85"))
                )
                logger.info("✅ 方法3: 评审信息区域已找到")
                time.sleep(2)
                return True
            except TimeoutException:
                logger.warning("⚠️ 方法3: 未找到评审信息区域，尝试其他方法")

            # 方法4: 等待信息编辑tab出现
            try:
                info_edit_tab = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, "tab-InfoEdit"))
                )
                logger.info("✅ 方法4: 信息编辑tab已找到")
                time.sleep(2)
                return True
            except TimeoutException:
                logger.warning("⚠️ 方法4: 未找到信息编辑tab，尝试其他方法")

            # 方法5: 通过文本内容检测页面加载
            try:
                text_indicators = [
                    "评审人信息", "评审信息", "评审人方案", "截止日期",
                    "编制信息", "文件上传", "文档信息"
                ]

                for indicator in text_indicators:
                    try:
                        element = WebDriverWait(self.driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{indicator}')]"))
                        )
                        if element.is_displayed():
                            logger.info(f"✅ 方法5: 通过文本 '{indicator}' 检测到页面已加载")
                            time.sleep(2)
                            return True
                    except:
                        continue
            except Exception as e:
                logger.warning(f"⚠️ 方法5失败: {str(e)}")

            # 方法6: 检查页面是否包含关键元素
            try:
                # 检查是否存在基本信息、评审文档、评审信息等区域
                key_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'el-collapse-item')]")
                if len(key_elements) >= 3:  # 至少有3个折叠区域
                    logger.info("✅ 方法6: 页面关键元素已加载")
                    time.sleep(2)
                    return True
                else:
                    logger.warning(f"⚠️ 方法6: 页面元素不完整，只找到 {len(key_elements)} 个区域")
            except Exception as e:
                logger.warning(f"⚠️ 方法6失败: {str(e)}")

            # 方法7: 检查表单元素是否加载完成
            try:
                form_elements = self.driver.find_elements(By.XPATH, "//form | //div[contains(@class, 'el-form')]")
                input_elements = self.driver.find_elements(By.XPATH, "//input | //textarea | //select")

                if len(form_elements) > 0 and len(input_elements) > 5:  # 至少有表单和多个输入元素
                    logger.info(f"✅ 方法7: 表单元素已加载完成 (表单:{len(form_elements)}, 输入框:{len(input_elements)})")
                    time.sleep(2)
                    return True
            except Exception as e:
                logger.warning(f"⚠️ 方法7失败: {str(e)}")

            # 方法8: 检查页面URL和标题
            try:
                current_url = self.driver.current_url
                page_title = self.driver.title

                # 检查URL是否包含预期的路径
                if any(keyword in current_url.lower() for keyword in ['document', 'review', 'approval', 'edit']):
                    logger.info(f"✅ 方法8: 通过URL检测页面已加载: {current_url}")
                    time.sleep(2)
                    return True

                # 检查页面标题
                if page_title and len(page_title) > 0:
                    logger.info(f"✅ 方法8: 通过页面标题检测页面已加载: {page_title}")
                    time.sleep(2)
                    return True
            except Exception as e:
                logger.warning(f"⚠️ 方法8失败: {str(e)}")

            # 方法9: JavaScript检测页面加载状态
            try:
                js_script = """
                // 检查页面加载状态
                if (document.readyState === 'complete') {
                    // 检查是否有Vue应用实例
                    if (window.Vue || document.querySelector('[data-v-]')) {
                        return {loaded: true, reason: 'Vue应用已加载'};
                    }

                    // 检查是否有Element UI组件
                    if (document.querySelector('.el-form, .el-button, .el-input')) {
                        return {loaded: true, reason: 'Element UI组件已加载'};
                    }

                    // 检查基本DOM结构
                    if (document.querySelectorAll('div').length > 50) {
                        return {loaded: true, reason: 'DOM结构已加载'};
                    }
                }

                return {loaded: false, reason: '页面未完全加载'};
                """

                result = self.driver.execute_script(js_script)
                if result.get('loaded'):
                    logger.info(f"✅ 方法9: JavaScript检测成功 - {result.get('reason')}")
                    time.sleep(2)
                    return True
            except Exception as e:
                logger.warning(f"⚠️ 方法9失败: {str(e)}")

            # 方法10: 简单等待，假设页面已经加载
            logger.info("⚠️ 所有检测方法都失败，使用简单等待策略")
            time.sleep(5)  # 增加等待时间
            logger.info("✅ 假设页面已加载完成，继续执行")
            return True

        except Exception as e:
            logger.error(f"❌ 页面加载检测失败: {str(e)}")
            # 即使检测失败，也返回True继续执行
            time.sleep(5)
            return True
    
    def _scroll_to_reviewer_section(self):
        """滚动到评审人信息区域 - 增强版多种定位方法"""
        try:
            logger.info("📜 滚动到评审人信息区域...")

            # 方法1: 尝试滚动到评审人信息区域 (新的ID)
            try:
                reviewer_section = self.driver.find_element(By.ID, "el-collapse-content-97")
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", reviewer_section)
                time.sleep(2)
                logger.info("✅ 方法1: 滚动到评审人信息区域成功")
                return True
            except Exception as e1:
                logger.warning(f"⚠️ 方法1失败: {str(e1)}")

            # 方法2: 通过文本内容查找评审人信息区域
            try:
                reviewer_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '评审人信息') or contains(text(), '评审人方案') or contains(text(), '评审方案')]")
                if reviewer_elements:
                    target_element = reviewer_elements[0]
                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", target_element)
                    time.sleep(2)
                    logger.info("✅ 方法2: 通过文本内容滚动成功")
                    return True
            except Exception as e2:
                logger.warning(f"⚠️ 方法2失败: {str(e2)}")

            # 方法3: 查找包含评审人方案的表单区域
            try:
                form_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'el-collapse-item')]//div[contains(@class, 'el-collapse-item__content')]")
                for element in form_elements:
                    try:
                        if "评审" in element.text or "方案" in element.text:
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
                            time.sleep(2)
                            logger.info("✅ 方法3: 通过表单区域滚动成功")
                            return True
                    except:
                        continue
            except Exception as e3:
                logger.warning(f"⚠️ 方法3失败: {str(e3)}")

            # 方法4: 尝试滚动到评审信息区域
            try:
                review_info_section = self.driver.find_element(By.ID, "el-collapse-content-85")
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", review_info_section)
                time.sleep(2)
                logger.info("✅ 方法4: 滚动到评审信息区域成功")
                return True
            except Exception as e4:
                logger.warning(f"⚠️ 方法4失败: {str(e4)}")

            # 方法5: 查找所有可能的评审相关元素
            try:
                selectors = [
                    "//label[contains(text(), '评审人方案')]",
                    "//div[contains(@class, 'el-select') and contains(@aria-controls, 'el-id')]",
                    "//input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id')]",
                    "//div[contains(@class, 'el-form-item') and .//label[contains(text(), '评审')]]"
                ]

                for selector in selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        if elements:
                            target = elements[0]
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", target)
                            time.sleep(2)
                            logger.info(f"✅ 方法5: 使用选择器 {selector} 滚动成功")
                            return True
                    except:
                        continue
            except Exception as e5:
                logger.warning(f"⚠️ 方法5失败: {str(e5)}")

            # 方法6: 分步滚动到页面底部
            try:
                # 先滚动到页面中部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 2);")
                time.sleep(1)
                # 再滚动到底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                logger.info("✅ 方法6: 分步滚动到页面底部成功")
                return True
            except Exception as e6:
                logger.warning(f"⚠️ 方法6失败: {str(e6)}")

            # 方法7: 强制滚动到超大数值
            try:
                self.driver.execute_script("window.scrollTo(0, 999999);")
                time.sleep(2)
                logger.info("✅ 方法7: 强制滚动成功")
                return True
            except Exception as e7:
                logger.warning(f"⚠️ 方法7失败: {str(e7)}")

            # 方法8: 尝试滚动页面容器
            try:
                containers = [
                    "el-scrollbar__view",
                    "el-scrollbar__wrap",
                    "main-content",
                    "page-content"
                ]

                for container_class in containers:
                    try:
                        container = self.driver.find_element(By.CLASS_NAME, container_class)
                        self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight;", container)
                        time.sleep(2)
                        logger.info(f"✅ 方法8: 滚动容器 {container_class} 成功")
                        return True
                    except:
                        continue
            except Exception as e8:
                logger.warning(f"⚠️ 方法8失败: {str(e8)}")

            # 方法9: 使用键盘操作滚动
            try:
                from selenium.webdriver.common.keys import Keys
                body = self.driver.find_element(By.TAG_NAME, "body")
                for _ in range(10):  # 按10次Page Down
                    body.send_keys(Keys.PAGE_DOWN)
                    time.sleep(0.5)
                logger.info("✅ 方法9: 键盘滚动成功")
                return True
            except Exception as e9:
                logger.warning(f"⚠️ 方法9失败: {str(e9)}")

            logger.warning("⚠️ 所有滚动方法都失败了，但继续尝试操作")
            return True

        except Exception as e:
            logger.error(f"❌ 滚动失败: {str(e)}")
            return False
    
    def _identify_file_type_from_doc_id(self, doc_id):
        """从文档编号识别文件类型"""
        try:
            logger.info(f"🔍 从文档编号识别文件类型: {doc_id}")
            
            # 从文档编号中提取文件类型
            if "_DVP_" in doc_id:
                file_type = "DVP"
            elif "_PPL_" in doc_id:
                file_type = "PPL"
            elif "_FN_" in doc_id:
                file_type = "FN"
            else:
                logger.error(f"❌ 无法识别文档编号 {doc_id} 的文件类型")
                return None
            
            logger.info(f"✅ 识别文件类型: {file_type}")
            return file_type
            
        except Exception as e:
            logger.error(f"❌ 文件类型识别失败: {str(e)}")
            return None
    
    def _select_reviewer_scheme(self, file_type):
        """选择对应的评审人方案"""
        try:
            logger.info(f"📝 选择 {file_type} 类型的评审人方案...")

            scheme_config = self.reviewer_schemes.get(file_type)
            if not scheme_config:
                logger.error(f"❌ 未找到 {file_type} 类型的评审人方案配置")
                return False

            target_scheme = scheme_config['scheme_name']
            logger.info(f"🎯 目标方案: {target_scheme}")

            # 点击评审人方案输入框
            logger.info("🖱️ 步骤1: 点击评审人方案输入框...")
            if not self._click_reviewer_scheme_input(scheme_config['scheme_xpath']):
                logger.error("❌ 步骤1失败: 无法点击评审人方案输入框")
                return False

            # 等待下拉框出现并选择对应选项
            logger.info("📋 步骤2: 选择下拉框选项...")
            if not self._select_scheme_dropdown_option(scheme_config['dropdown_xpath'], target_scheme):
                logger.error("❌ 步骤2失败: 无法选择下拉框选项")
                return False

            # 等待方案加载完成
            time.sleep(3)

            # 验证选择是否成功
            logger.info("✅ 步骤3: 验证方案选择结果...")
            if self._verify_scheme_selection(target_scheme):
                logger.info(f"✅ {file_type} 评审人方案选择成功: {target_scheme}")
                return True
            else:
                logger.warning(f"⚠️ {file_type} 评审人方案选择可能未成功，但继续执行")
                return True  # 即使验证失败也继续，避免阻塞流程

        except Exception as e:
            logger.error(f"❌ 评审人方案选择失败: {str(e)}")
            return False
    
    def _click_reviewer_scheme_input(self, scheme_xpath):
        """点击评审人方案输入框 - 增强版多种定位方法，支持单个xpath或xpath列表"""
        try:
            logger.info("🖱️ 点击评审人方案输入框...")

            # 处理scheme_xpath可能是字符串或列表的情况
            if isinstance(scheme_xpath, str):
                xpath_list = [scheme_xpath]
            else:
                xpath_list = scheme_xpath

            # 方法1: 使用提供的xpath列表
            for i, xpath in enumerate(xpath_list, 1):
                try:
                    element = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, xpath))
                    )
                    element.click()
                    time.sleep(2)
                    logger.info(f"✅ 方法1.{i}: 使用xpath点击成功: {xpath[:50]}...")
                    return True
                except Exception as e:
                    logger.debug(f"⚠️ 方法1.{i}失败: {str(e)}")
                    continue

            # 方法2: 通过文本内容查找评审人方案标签
            try:
                label_selectors = [
                    "//label[contains(text(), '评审人方案')]",
                    "//label[contains(text(), '评审方案')]",
                    "//span[contains(text(), '评审人方案')]",
                    "//*[contains(text(), '评审人方案')]"
                ]

                for selector in label_selectors:
                    try:
                        label_element = self.driver.find_element(By.XPATH, selector)
                        # 查找标签后面的输入框
                        input_element = label_element.find_element(By.XPATH, ".//following-sibling::div//input | .//following-sibling::div//div[contains(@class, 'el-select')]")
                        input_element.click()
                        time.sleep(2)
                        logger.info(f"✅ 方法2: 通过标签 {selector} 找到输入框并点击成功")
                        return True
                    except:
                        continue
            except Exception as e:
                logger.warning(f"⚠️ 方法2失败: {str(e)}")

            # 方法3: 查找所有可能的评审人方案输入框 - 更精确的定位
            try:
                input_selectors = [
                    # 优先使用更精确的选择器，避免点击到其他输入框
                    "//label[contains(text(), '评审人方案')]/following-sibling::div//input[contains(@class, 'el-select__input')]",
                    "//div[contains(@class, 'el-form-item') and .//label[contains(text(), '评审人方案')]]//input[contains(@class, 'el-select__input')]",
                    "//input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id-') and ancestor::div[.//label[contains(text(), '评审人方案')]]]",
                    "//input[contains(@class, 'el-select__input') and ancestor::div[.//label[contains(text(), '评审人方案')]]]",
                    # 备用选择器
                    "//input[contains(@id, 'el-id-') and contains(@aria-controls, 'el-id-')]",
                    "//input[contains(@class, 'el-select__input')]",
                    "//div[contains(@class, 'el-select__wrapper')]",
                    "//div[contains(@class, 'el-select') and contains(@class, 'el-select--large')]"
                ]

                for i, selector in enumerate(input_selectors):
                    try:
                        logger.debug(f"尝试选择器 {i+1}: {selector}")
                        elements = self.driver.find_elements(By.XPATH, selector)

                        for element in elements:
                            if element.is_displayed() and element.is_enabled():
                                # 对于前几个精确选择器，直接点击
                                if i < 4:  # 前4个是精确选择器
                                    element.click()
                                    time.sleep(2)
                                    logger.info(f"✅ 方法3: 使用精确选择器 {i+1} 点击成功")
                                    return True
                                else:
                                    # 对于备用选择器，检查是否在评审相关的区域内
                                    try:
                                        parent_text = element.find_element(By.XPATH, "./ancestor::div[contains(@class, 'el-form-item')]").text
                                        if "评审" in parent_text or "方案" in parent_text:
                                            element.click()
                                            time.sleep(2)
                                            logger.info(f"✅ 方法3: 使用备用选择器 {i+1} 点击成功")
                                            return True
                                    except:
                                        # 如果无法获取父元素文本，跳过这个元素
                                        continue
                    except Exception as e:
                        logger.debug(f"选择器 {i+1} 失败: {str(e)}")
                        continue
            except Exception as e:
                logger.warning(f"⚠️ 方法3失败: {str(e)}")

            # 方法4: 使用CSS选择器 (基于新的HTML结构)
            try:
                css_selectors = [
                    "#el-collapse-content-97 div.el-select__wrapper",
                    "#el-collapse-content-97 .el-select__selection",
                    "#el-collapse-content-97 .el-select__input-wrapper",
                    "#el-collapse-content-97 input.el-select__input",
                    "input[id='el-id-9858-115']",  # 基于HTML中的具体ID
                    "label[for='el-id-9858-115'] + div .el-select__wrapper",
                    ".el-select__wrapper[tabindex='-1']",
                    ".el-select__selection .el-select__input-wrapper"
                ]

                for selector in css_selectors:
                    try:
                        element = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                        element.click()
                        time.sleep(2)
                        logger.info(f"✅ 方法4: CSS选择器 '{selector}' 点击成功")
                        return True
                    except:
                        continue
            except Exception as e:
                logger.warning(f"⚠️ 方法4失败: {str(e)}")

            # 方法5: JavaScript深度搜索和点击
            try:
                js_script = """
                // 查找所有可能的评审人方案输入框
                var candidates = [];

                // 方法1: 通过文本内容查找
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var el = allElements[i];
                    if (el.textContent && (el.textContent.includes('评审人方案') || el.textContent.includes('评审方案'))) {
                        // 查找相关的输入框
                        var inputs = el.querySelectorAll('input, .el-select, .el-select__wrapper');
                        for (var j = 0; j < inputs.length; j++) {
                            candidates.push(inputs[j]);
                        }
                    }
                }

                // 方法2: 直接查找select相关元素
                var selects = document.querySelectorAll('.el-select__wrapper, .el-select__input, input[aria-controls]');
                for (var k = 0; k < selects.length; k++) {
                    candidates.push(selects[k]);
                }

                // 尝试点击候选元素
                for (var m = 0; m < candidates.length; m++) {
                    var candidate = candidates[m];
                    if (candidate && candidate.offsetParent !== null) { // 检查是否可见
                        try {
                            candidate.click();
                            return true;
                        } catch (e) {
                            continue;
                        }
                    }
                }
                return false;
                """

                result = self.driver.execute_script(js_script)
                if result:
                    time.sleep(2)
                    logger.info("✅ 方法5: JavaScript深度搜索点击成功")
                    return True
            except Exception as e:
                logger.warning(f"⚠️ 方法5失败: {str(e)}")

            # 方法6: 坐标点击法
            try:
                # 查找包含"评审"文字的元素
                review_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '评审')]")
                for element in review_elements:
                    try:
                        # 获取元素位置和大小
                        location = element.location
                        size = element.size

                        # 计算点击位置（元素右侧）
                        click_x = location['x'] + size['width'] + 100
                        click_y = location['y'] + size['height'] // 2

                        # 使用ActionChains进行坐标点击
                        from selenium.webdriver.common.action_chains import ActionChains
                        actions = ActionChains(self.driver)
                        actions.move_by_offset(click_x, click_y).click().perform()
                        time.sleep(2)
                        logger.info("✅ 方法6: 坐标点击成功")
                        return True
                    except:
                        continue
            except Exception as e:
                logger.warning(f"⚠️ 方法6失败: {str(e)}")

            # 方法7: 模拟键盘操作
            try:
                from selenium.webdriver.common.keys import Keys
                # 尝试使用Tab键导航到输入框
                body = self.driver.find_element(By.TAG_NAME, "body")
                for _ in range(20):  # 最多按20次Tab
                    body.send_keys(Keys.TAB)
                    time.sleep(0.3)

                    # 检查当前焦点元素是否是我们要找的输入框
                    active_element = self.driver.switch_to.active_element
                    if active_element:
                        class_name = active_element.get_attribute('class') or ''
                        if 'el-select' in class_name or 'input' in active_element.tag_name.lower():
                            active_element.send_keys(Keys.ENTER)
                            time.sleep(2)
                            logger.info("✅ 方法7: 键盘导航点击成功")
                            return True
            except Exception as e:
                logger.warning(f"⚠️ 方法7失败: {str(e)}")

            logger.error("❌ 所有点击方法都失败了")
            return False

        except Exception as e:
            logger.error(f"❌ 点击评审人方案输入框失败: {str(e)}")
            return False
    
    def _select_scheme_dropdown_option(self, dropdown_xpath, scheme_name):
        """选择方案下拉框选项 - 增强版多种定位方法"""
        try:
            logger.info(f"📋 选择方案下拉框选项: {scheme_name}")

            # 等待下拉框出现
            time.sleep(3)

            # 方法1: 使用精确xpath
            try:
                option_element = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, dropdown_xpath))
                )
                option_element.click()
                time.sleep(2)
                logger.info("✅ 方法1: 使用精确xpath选择成功")
                return True
            except Exception as e1:
                logger.warning(f"⚠️ 方法1失败: {str(e1)}")

            # 方法2: 查找包含关键词的选项 - 精确匹配方案名称
            try:
                # 根据方案名称精确匹配，避免选择错误的选项
                target_scheme = scheme_name.strip()
                logger.info(f"🎯 寻找精确匹配的方案: {target_scheme}")

                dropdown_selectors = [
                    "//div[contains(@class, 'el-select-dropdown')]//li",
                    "//ul[contains(@class, 'el-select-dropdown__list')]//li",
                    "//div[contains(@class, 'el-popper')]//li",
                    "//*[contains(@class, 'dropdown')]//li",
                    "//li[@role='option']"
                ]

                for selector in dropdown_selectors:
                    try:
                        dropdown_items = WebDriverWait(self.driver, 3).until(
                            EC.presence_of_all_elements_located((By.XPATH, selector))
                        )

                        for item in dropdown_items:
                            if item.is_displayed():
                                item_text = item.text.strip()
                                logger.debug(f"检查选项: '{item_text}'")

                                # 精确匹配或包含所有关键词
                                if item_text == target_scheme or self._scheme_matches(item_text, target_scheme):
                                    logger.info(f"🎯 找到匹配的方案选项: '{item_text}'")
                                    item.click()
                                    time.sleep(2)
                                    logger.info(f"✅ 方法2: 精确匹配选择成功")
                                    return True
                    except:
                        continue

            except Exception as e2:
                logger.warning(f"⚠️ 方法2失败: {str(e2)}")

            # 方法3: JavaScript深度搜索选择
            try:
                js_script = """
                var keywords = ['数据管理员', '部门相关方', '系统专家', '项目主管', '品质主管', '品质经理'];

                // 查找所有可能的下拉选项
                var selectors = [
                    '.el-select-dropdown__list li',
                    '.el-select-dropdown li',
                    '.el-popper li',
                    'li[role="option"]',
                    '[class*="dropdown"] li',
                    '[class*="option"]'
                ];

                for (var s = 0; s < selectors.length; s++) {
                    var items = document.querySelectorAll(selectors[s]);
                    for (var i = 0; i < items.length; i++) {
                        var item = items[i];
                        if (item.offsetParent !== null) { // 检查是否可见
                            var text = item.textContent || item.innerText || '';
                            for (var k = 0; k < keywords.length; k++) {
                                if (text.includes(keywords[k])) {
                                    try {
                                        item.click();
                                        return {success: true, keyword: keywords[k], text: text};
                                    } catch (e) {
                                        continue;
                                    }
                                }
                            }
                        }
                    }
                }
                return {success: false};
                """

                result = self.driver.execute_script(js_script)
                if result.get('success'):
                    time.sleep(2)
                    logger.info(f"✅ 方法3: JavaScript选择成功，关键词: {result.get('keyword')}")
                    return True

            except Exception as e3:
                logger.warning(f"⚠️ 方法3失败: {str(e3)}")

            # 方法4: 查找所有可见的li元素并尝试点击第一个
            try:
                all_li_elements = self.driver.find_elements(By.TAG_NAME, "li")
                visible_li = [li for li in all_li_elements if li.is_displayed() and li.text.strip()]

                if visible_li:
                    # 优先选择包含评审相关文字的选项
                    for li in visible_li:
                        text = li.text.strip()
                        if any(word in text for word in ["数据", "管理", "评审", "方案", "专家", "主管"]):
                            li.click()
                            time.sleep(2)
                            logger.info(f"✅ 方法4: 选择包含关键词的选项成功: {text}")
                            return True

                    # 如果没有找到关键词，选择第一个可见选项
                    visible_li[0].click()
                    time.sleep(2)
                    logger.info(f"✅ 方法4: 选择第一个可见选项成功: {visible_li[0].text.strip()}")
                    return True

            except Exception as e4:
                logger.warning(f"⚠️ 方法4失败: {str(e4)}")

            # 方法5: 使用键盘操作选择
            try:
                from selenium.webdriver.common.keys import Keys

                # 获取当前活动元素
                active_element = self.driver.switch_to.active_element

                # 尝试键盘导航
                keyboard_sequences = [
                    [Keys.ARROW_DOWN, Keys.ENTER],
                    [Keys.ENTER],
                    [Keys.SPACE],
                    [Keys.ARROW_DOWN, Keys.ARROW_DOWN, Keys.ENTER],
                    [Keys.HOME, Keys.ENTER]  # 选择第一个选项
                ]

                for sequence in keyboard_sequences:
                    try:
                        for key in sequence:
                            active_element.send_keys(key)
                            time.sleep(0.5)

                        time.sleep(2)
                        logger.info(f"✅ 方法5: 键盘序列 {sequence} 选择成功")
                        return True

                    except Exception:
                        continue

            except Exception as e5:
                logger.warning(f"⚠️ 方法5失败: {str(e5)}")

            # 方法6: 坐标点击法
            try:
                # 查找下拉框区域
                dropdown_areas = self.driver.find_elements(By.XPATH, "//*[contains(@class, 'dropdown') or contains(@class, 'popper')]")

                for area in dropdown_areas:
                    if area.is_displayed():
                        try:
                            # 获取区域位置和大小
                            location = area.location
                            size = area.size

                            # 点击区域内的第一个选项位置
                            click_x = location['x'] + 50
                            click_y = location['y'] + 30

                            from selenium.webdriver.common.action_chains import ActionChains
                            actions = ActionChains(self.driver)
                            actions.move_by_offset(click_x, click_y).click().perform()
                            time.sleep(2)
                            logger.info("✅ 方法6: 坐标点击选择成功")
                            return True
                        except:
                            continue

            except Exception as e6:
                logger.warning(f"⚠️ 方法6失败: {str(e6)}")

            logger.error("❌ 所有选择方法都失败了")
            return False

        except Exception as e:
            logger.error(f"❌ 选择下拉框选项失败: {str(e)}")
            return False

    def _scheme_matches(self, item_text, target_scheme):
        """检查选项文本是否匹配目标方案"""
        try:
            # 提取目标方案中的关键词
            target_keywords = target_scheme.replace("、", ",").split(",")
            target_keywords = [kw.strip() for kw in target_keywords if kw.strip()]

            # 检查是否包含所有关键词
            matches = 0
            for keyword in target_keywords:
                if keyword in item_text:
                    matches += 1

            # 如果匹配了大部分关键词（至少70%），认为是匹配的
            match_ratio = matches / len(target_keywords) if target_keywords else 0
            is_match = match_ratio >= 0.7

            if is_match:
                logger.info(f"✅ 方案匹配成功: '{item_text}' 匹配 '{target_scheme}' (匹配率: {match_ratio:.2f})")

            return is_match

        except Exception as e:
            logger.warning(f"⚠️ 方案匹配检查失败: {str(e)}")
            return False

    def _should_preserve_existing_content(self, input_element, role_name):
        """判断是否应该保留输入框中的现有内容"""
        try:
            # 检查是否是多选框
            element_class = input_element.get_attribute('class') or ''
            is_multiselect = 'el-select__input' in element_class

            # 检查是否有现有内容
            current_value = input_element.get_attribute('value') or ''
            placeholder = input_element.get_attribute('placeholder') or ''
            has_content = current_value.strip() != '' and current_value != placeholder

            # 检查是否是数据管理员角色（特别容易出现重复点击问题）
            is_data_manager = '数据管理员' in role_name

            # 如果是多选框且已有内容，特别是数据管理员，应该保留
            should_preserve = is_multiselect and has_content and is_data_manager

            if should_preserve:
                logger.info(f"🔒 保留 {role_name} 输入框现有内容: '{current_value}'")

            return should_preserve

        except Exception as e:
            logger.warning(f"⚠️ 检查是否保留内容失败: {str(e)}")
            return False

    def _verify_scheme_selection(self, target_scheme):
        """验证评审人方案是否选择成功"""
        try:
            logger.info(f"🔍 验证方案选择: {target_scheme}")

            # 方法1: 检查输入框的值
            try:
                scheme_inputs = self.driver.find_elements(By.XPATH, "//input[contains(@class, 'el-select__input')]")
                for input_elem in scheme_inputs:
                    if input_elem.is_displayed():
                        value = input_elem.get_attribute('value') or ''
                        if target_scheme in value or self._scheme_matches(value, target_scheme):
                            logger.info(f"✅ 方法1: 通过输入框值验证成功: '{value}'")
                            return True
            except Exception as e:
                logger.debug(f"方法1失败: {str(e)}")

            # 方法2: 检查页面上是否出现了相关的角色表格
            try:
                # 等待角色表格出现
                time.sleep(2)
                role_elements = self.driver.find_elements(By.XPATH, "//span[contains(text(), '数据管理员') or contains(text(), '相关方') or contains(text(), '科长')]")
                if role_elements:
                    logger.info(f"✅ 方法2: 检测到角色表格，找到 {len(role_elements)} 个角色元素")
                    return True
            except Exception as e:
                logger.debug(f"方法2失败: {str(e)}")

            # 方法3: 检查是否有评审人输入框出现
            try:
                reviewer_inputs = self.driver.find_elements(By.XPATH, "//tbody//tr//input[contains(@class, 'el-select__input')]")
                if len(reviewer_inputs) >= 2:  # PPL至少应该有3个角色
                    logger.info(f"✅ 方法3: 检测到 {len(reviewer_inputs)} 个评审人输入框")
                    return True
            except Exception as e:
                logger.debug(f"方法3失败: {str(e)}")

            logger.warning("⚠️ 所有验证方法都未能确认方案选择成功")
            return False

        except Exception as e:
            logger.warning(f"⚠️ 验证方案选择失败: {str(e)}")
            return False
    

    
    def _switch_to_second_page_tab(self):
        """检查并切换到第二个页面的标签页"""
        try:
            logger.info("🔄 检查标签页情况...")
            
            # 获取当前所有标签页
            all_windows = self.driver.window_handles
            current_window = self.driver.current_window_handle
            
            logger.info(f"当前有 {len(all_windows)} 个标签页")
            
            # 如果只有一个标签页，说明第二个页面在同一标签页
            if len(all_windows) == 1:
                logger.info("✅ 第二个页面在当前标签页，无需切换")
                return True
            
            # 如果有多个标签页，需要切换到最新的标签页
            if len(all_windows) > 1:
                # 切换到最新打开的标签页
                new_window = all_windows[-1]
                if new_window != current_window:
                    self.driver.switch_to.window(new_window)
                    time.sleep(2)
                    logger.info("✅ 已切换到新标签页")
                    
                    # 验证是否是第二个页面
                    if self._verify_second_page():
                        logger.info("✅ 确认当前是第二个页面")
                        return True
                    else:
                        logger.warning("⚠️ 新标签页不是第二个页面，尝试其他标签页")
                        
                        # 尝试其他标签页
                        for window in all_windows:
                            if window != current_window:
                                try:
                                    self.driver.switch_to.window(window)
                                    time.sleep(1)
                                    if self._verify_second_page():
                                        logger.info("✅ 找到第二个页面标签页")
                                        return True
                                except Exception as e:
                                    logger.warning(f"⚠️ 切换标签页失败: {str(e)}")
                                    continue
                        
                        logger.error("❌ 未找到第二个页面标签页")
                        return False
                else:
                    logger.info("✅ 已在正确的标签页")
                    return True
            
            logger.warning("⚠️ 标签页状态异常，尝试继续执行")
            return True
            
        except Exception as e:
            logger.error(f"❌ 标签页切换失败: {str(e)}")
            return False
    
    def _verify_second_page(self):
        """验证当前页面是否是第二个页面 - 基于新的HTML结构"""
        try:
            # 方法1: 检查是否存在最外层容器
            try:
                self.driver.find_element(By.XPATH, "//*[@id='app']/div/div/div[2]/div/div[2]/div[2]")
                return True
            except NoSuchElementException:
                pass

            # 方法2: 检查是否存在评审人信息区域 (新的ID)
            try:
                self.driver.find_element(By.ID, "el-collapse-content-97")
                return True
            except NoSuchElementException:
                pass

            # 方法3: 检查是否存在评审信息区域
            try:
                self.driver.find_element(By.ID, "el-collapse-content-85")
                return True
            except NoSuchElementException:
                pass

            # 方法4: 检查是否存在信息编辑tab
            try:
                self.driver.find_element(By.ID, "tab-InfoEdit")
                return True
            except NoSuchElementException:
                pass

            # 方法5: 检查是否存在评审人方案标签
            try:
                self.driver.find_element(By.XPATH, "//label[contains(text(), '评审人方案')]")
                return True
            except NoSuchElementException:
                pass

            # 方法6: 检查页面标题或URL
            try:
                current_url = self.driver.current_url
                page_title = self.driver.title

                # 如果URL或标题包含评审相关关键词
                if any(keyword in current_url.lower() for keyword in ['review', 'approval', 'audit', '评审']):
                    return True
                if any(keyword in page_title.lower() for keyword in ['review', 'approval', 'audit', '评审']):
                    return True
            except Exception:
                pass

            # 方法7: 检查是否存在基本的第二页面元素
            try:
                elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'el-collapse-item')]")
                if len(elements) >= 3:  # 第二个页面通常有多个折叠区域
                    return True
            except Exception:
                pass

            return False

        except Exception as e:
            logger.warning(f"⚠️ 页面验证失败: {str(e)}")
            return False
        
    def _load_excel_data(self):
        """读取Excel数据"""
        try:
            logger.info("📊 读取Excel数据...")
            
            data_folder = Path("Data")
            if not data_folder.exists():
                logger.error("❌ Data文件夹不存在")
                return None
            
            # 查找最新的xlsx文件
            xlsx_files = list(data_folder.glob("*.xlsx"))
            if not xlsx_files:
                logger.error("❌ Data文件夹中没有xlsx文件")
                return None
            
            # 按修改时间排序，取最新的
            latest_file = max(xlsx_files, key=lambda f: f.stat().st_mtime)
            logger.info(f"📄 使用Excel文件: {latest_file.name}")
            
            # 读取Info sheet
            try:
                df = pd.read_excel(latest_file, sheet_name='Info')
                logger.info(f"✅ 成功读取Excel数据，共 {len(df)} 行")
                return df
            except Exception as e:
                logger.error(f"❌ 读取Excel文件失败: {str(e)}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 加载Excel数据失败: {str(e)}")
            return None
    
    def _get_fn_related_role(self, filename):
        """根据FN文件名确定相关方角色 - 增强DiSus系列识别"""
        try:
            logger.info(f"🔍 收到的文件名参数: '{filename}' (类型: {type(filename)})")
            
            if not filename:
                logger.info("🔍 FN文件名为空，使用默认角色: FN_IMU")
                return "FN_IMU"  # 默认值
            
            logger.info(f"🔍 分析FN文件名: {filename}")
            
            # 按优先级顺序匹配，DiSus系列优先
            for role, (keyword_pattern, required_text) in self.fn_file_patterns.items():
                try:
                    # 检查是否包含接口定义通知单
                    if required_text not in filename:
                        continue
                        
                    # 使用正则表达式匹配关键词
                    if re.search(keyword_pattern, filename, re.IGNORECASE):
                        logger.info(f"✅ FN文件 {filename} 匹配到角色: {role}")
                        return role
                        
                except Exception as e:
                    logger.debug(f"匹配角色 {role} 时出错: {str(e)}")
                    continue
            
            logger.warning(f"⚠️ FN文件 {filename} 未匹配到特定角色，使用默认: FN_IMU")
            return "FN_IMU"
            
        except Exception as e:
            logger.error(f"❌ 分析FN文件名失败: {str(e)}")
            return "FN_IMU"
    
    def _extract_people_and_emails(self, df, role_key):
        """从Excel中提取指定角色的人员和邮箱"""
        try:
            logger.info(f"🔍 查找Excel中的角色: {role_key}")
            
            # 检查DataFrame是否有效
            if df is None or df.empty:
                logger.error("❌ DataFrame为空或无效")
                return []
            
            # 检查必要的列是否存在
            required_columns = ['role', 'people', 'mail']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"❌ Excel缺少必要的列: {missing_columns}")
                return []
            
            # 查找角色行
            role_row = df[df['role'] == role_key]
            if role_row.empty:
                logger.warning(f"⚠️ Excel中未找到角色: {role_key}")
                return []
            
            logger.info(f"✅ 找到角色 {role_key}，行索引: {role_row.index[0]}")
            
            # 获取people和mail列的值
            people_value = role_row['people'].iloc[0]
            mail_value = role_row['mail'].iloc[0]
            
            logger.info(f"📝 people值: {people_value}")
            logger.info(f"📧 mail值: {mail_value}")
            
            # 处理people列（可能包含多个人名，用各种分隔符分隔）
            people_list = []
            if pd.notna(people_value) and str(people_value).strip():
                # 使用多种分隔符分割人名
                people_str = str(people_value).strip()
                # 支持逗号、分号、空格等分隔符
                people_list = re.split(r'[,，;；\s]+', people_str)
                people_list = [name.strip() for name in people_list if name.strip()]
            
            # 处理mail列（可能横向扩展到多个单元格）
            email_list = []
            if pd.notna(mail_value) and str(mail_value).strip():
                email_list.append(str(mail_value).strip())
            
            # 检查横向扩展的邮箱
            role_index = role_row.index[0]
            mail_col_index = df.columns.get_loc('mail')
            
            # 向右查找更多邮箱
            for i in range(mail_col_index + 1, len(df.columns)):
                if i < len(df.columns):
                    next_mail = df.iloc[role_index, i]
                    if pd.notna(next_mail) and str(next_mail).strip():
                        email_list.append(str(next_mail).strip())
                    else:
                        break
            
            logger.info(f"👥 people_list: {people_list}")
            logger.info(f"📧 email_list: {email_list}")
            
            # 组合人员和邮箱
            result = []
            for i in range(max(len(people_list), len(email_list))):
                person_info = {}
                if i < len(people_list):
                    person_info['name'] = people_list[i]
                if i < len(email_list):
                    person_info['email'] = email_list[i]
                
                # 至少要有姓名或邮箱之一
                if person_info:
                    result.append(person_info)
            
            logger.info(f"✅ 角色 {role_key} 找到 {len(result)} 个人员: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ 提取角色 {role_key} 数据失败: {str(e)}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return []
    
    def _fill_reviewers(self, file_type, excel_data, filename=None):
        """填写评审人"""
        try:
            logger.info(f"👥 开始填写 {file_type} 类型的评审人...")
            
            scheme_config = self.reviewer_schemes.get(file_type)
            if not scheme_config:
                logger.error(f"❌ 未找到 {file_type} 类型的配置")
                return False
            
            # 等待页面稳定
            time.sleep(3)
            
            # 遍历每个角色
            for role_info in scheme_config['roles']:
                role_name = role_info['name']
                role_xpath = role_info['xpath']
                excel_role = role_info['excel_role']
                
                # 处理FN相关方的动态角色
                if file_type == "FN" and excel_role == "dynamic":
                    excel_role = self._get_fn_related_role(filename)
                
                logger.info(f"📝 填写角色: {role_name} (Excel角色: {excel_role})")
                
                # 从Excel获取人员信息
                people_info = self._extract_people_and_emails(excel_data, excel_role)
                if not people_info:
                    logger.warning(f"⚠️ 角色 {role_name} 没有找到人员信息，跳过")
                    continue
                
                # 填写该角色的所有人员
                if not self._fill_role_people(role_xpath, people_info, role_name):
                    logger.error(f"❌ 填写角色 {role_name} 失败")
                    return False
            
            logger.info(f"✅ {file_type} 类型评审人填写完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 填写评审人失败: {str(e)}")
            return False
    
    def _fill_role_people(self, role_xpath, people_info, role_name):
        """为单个角色填写多个人员 - 优化多人员输入逻辑"""
        try:
            logger.info(f"👤 为角色 {role_name} 填写 {len(people_info)} 个人员...")

            for i, person in enumerate(people_info):
                logger.info(f"🔄 处理第 {i+1}/{len(people_info)} 个人员...")

                # 查找输入框 - 每次重新定位确保准确性，使用多种定位方式
                input_element = self._find_role_input_element(role_xpath, role_name)
                if not input_element:
                    logger.error(f"❌ 未找到角色 {role_name} 的输入框")
                    return False
                
                # 准备搜索文本
                search_text = person.get('email', person.get('name', ''))
                if not search_text:
                    logger.warning(f"⚠️ 人员信息不完整，跳过: {person}")
                    continue
                
                # 过滤含有中文等非字母数字符号的文本
                if not self._is_valid_search_text(search_text):
                    logger.warning(f"⚠️ 搜索文本包含无效字符，跳过: {search_text}")
                    continue
                
                logger.info(f"🔍 输入人员信息: {search_text}")
                
                # 智能点击输入框 - 针对多选下拉框优化
                try:
                    # 滚动到输入框
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", input_element)
                    time.sleep(1)
                    
                    # 多种点击方式确保输入框激活
                    click_methods = [
                        # 方法1: 直接点击
                        lambda: input_element.click(),
                        # 方法2: JavaScript点击
                        lambda: self.driver.execute_script("arguments[0].click();", input_element),
                        # 方法3: 点击输入框内部（针对Element UI多选框）
                        lambda: self.driver.execute_script("""
                            var el = arguments[0];
                            var rect = el.getBoundingClientRect();
                            var event = new MouseEvent('click', {
                                view: window,
                                bubbles: true,
                                cancelable: true,
                                clientX: rect.left + 10,
                                clientY: rect.top + rect.height / 2
                            });
                            el.dispatchEvent(event);
                        """, input_element),
                        # 方法4: 使用Actions点击
                        lambda: self._action_click_element(input_element)
                    ]
                    
                    clicked_successfully = False
                    for method_index, click_method in enumerate(click_methods):
                        try:
                            click_method()
                            time.sleep(2)
                            
                            # 验证输入框是否已激活（通过检查焦点或某些属性）
                            if self._is_input_active(input_element):
                                logger.info(f"✅ 方法{method_index+1}成功激活输入框")
                                clicked_successfully = True
                                break
                            else:
                                # 即使验证失败，也认为点击成功，继续后续操作
                                logger.info(f"✅ 方法{method_index+1}点击完成，继续操作")
                                clicked_successfully = True
                                break
                                
                        except Exception as e:
                            logger.debug(f"点击方法{method_index+1}失败: {str(e)}")
                            continue
                    
                    if not clicked_successfully:
                        logger.warning(f"⚠️ 无法激活输入框，尝试继续...")
                    
                except Exception as e:
                    logger.warning(f"⚠️ 点击输入框失败: {str(e)}")
                
                # 智能处理输入框内容 - 避免清空已有数据
                try:
                    # 检查是否应该保留现有内容
                    should_preserve = self._should_preserve_existing_content(input_element, role_name)
                    current_value = input_element.get_attribute('value') or ''
                    placeholder = input_element.get_attribute('placeholder') or ''

                    logger.info(f"🔍 当前输入框状态 - 值: '{current_value}', 占位符: '{placeholder}', 保留内容: {should_preserve}")

                    if should_preserve:
                        logger.info(f"🔒 保留现有内容，跳过重复添加: {search_text}")
                        # 检查是否已经包含了要添加的人员
                        if search_text in current_value:
                            logger.info(f"✅ 人员 {search_text} 已存在，跳过添加")
                            continue
                        else:
                            logger.info(f"🔄 添加新人员到现有内容: {search_text}")
                            # 对于多选框，直接输入新内容，不清空
                            input_element.send_keys(search_text)
                    else:
                        logger.info(f"🆕 清空输入框后输入: {search_text}")
                        # 清空输入框 - 多种方式
                        clear_methods = [
                            lambda: input_element.clear(),
                            lambda: input_element.send_keys(""),
                            lambda: self.driver.execute_script("arguments[0].value = '';", input_element)
                        ]

                        for clear_method in clear_methods:
                            try:
                                clear_method()
                                time.sleep(1)  # 等待清空完成
                                break
                            except Exception:
                                continue

                        # 输入搜索文本
                        input_element.send_keys(search_text)

                    time.sleep(3)  # 等待搜索结果出现
                    logger.info(f"✅ 成功输入搜索文本: {search_text}")

                except Exception as e:
                    logger.error(f"❌ 输入搜索文本失败: {str(e)}")
                    continue
                
                # 选择搜索结果
                if not self._select_person_from_dropdown(search_text):
                    logger.warning(f"⚠️ 未找到人员 {search_text} 的搜索结果")
                    continue

                logger.info(f"✅ 成功添加人员: {search_text}")

                # 点击"评审角色"或"评审人"文字让填写的人员生效
                self._click_review_header_to_activate()

                # 如果还有更多人员，准备下一次输入
                if i < len(people_info) - 1:
                    try:
                        logger.info(f"🔄 准备添加下一个人员 ({i+2}/{len(people_info)})...")

                        # 等待页面稳定
                        time.sleep(3)

                        # Element UI 多选框需要特殊处理
                        # 重新激活输入框以便添加下一个选项
                        self._prepare_for_next_person_input(input_element, role_xpath)

                    except Exception as e:
                        logger.warning(f"⚠️ 准备下一个人员输入失败: {str(e)}")
            
            logger.info(f"✅ 角色 {role_name} 的 {len(people_info)} 个人员填写完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 填写角色 {role_name} 人员失败: {str(e)}")
            return False

    def _find_role_input_element(self, role_xpath, role_name):
        """使用多种定位方式查找角色输入框，支持单个xpath或xpath列表"""
        try:
            logger.info(f"🔍 查找角色 {role_name} 的输入框...")

            # 等待页面稳定
            time.sleep(2)

            # 处理role_xpath可能是字符串或列表的情况
            if isinstance(role_xpath, str):
                xpath_list = [role_xpath]
            else:
                xpath_list = role_xpath

            # 构建多种方式定位输入框 - 从最精确到最宽泛
            input_selectors = []

            # 添加提供的xpath列表
            for xpath in xpath_list:
                input_selectors.extend([
                    # 原始xpath
                    xpath,
                    # 确保选择第一个匹配项
                    f"({xpath})[1]",
                    # 更深层搜索
                    xpath.replace("//", "//div//") if "//" in xpath else xpath,
                ])

            # 添加通用备用选择器
            input_selectors.extend([
                # 基于角色名称的文本匹配定位
                f"//span[contains(text(), '{role_name}')]/ancestor::tr//input[@class='el-select__input']",

                # 基于表格结构定位
                f"//td[.//span[contains(text(), '{role_name}')]]/following-sibling::td//input[@class='el-select__input']",

                # 基于placeholder文本定位
                "//input[@placeholder='请输入工号/姓名' and @class='el-select__input']",

                # 基于aria-controls属性定位
                "//input[@role='combobox' and @class='el-select__input']",

                # 基于Element UI选择器结构定位
                "//div[contains(@class, 'el-select__wrapper')]//input[@class='el-select__input']",

                # 通用输入框定位（最后备选）
                "//input[contains(@class, 'el-select__input') and @type='text']"
            ])

            input_element = None

            for i, selector in enumerate(input_selectors):
                try:
                    logger.debug(f"尝试选择器 {i+1}: {selector}")

                    # 查找所有匹配的元素
                    elements = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_all_elements_located((By.XPATH, selector))
                    )

                    # 过滤出可见且可交互的元素
                    visible_elements = []
                    for element in elements:
                        try:
                            if element.is_displayed() and element.is_enabled():
                                # 检查元素是否在视口内
                                location = element.location
                                size = element.size
                                if location['x'] >= 0 and location['y'] >= 0 and size['width'] > 0 and size['height'] > 0:
                                    visible_elements.append(element)
                        except Exception:
                            continue

                    if visible_elements:
                        # 如果有多个匹配元素，选择第一个
                        input_element = visible_elements[0]
                        logger.info(f"✅ 使用选择器 {i+1} 找到输入框: {selector}")
                        break

                except TimeoutException:
                    logger.debug(f"选择器 {i+1} 超时")
                    continue
                except Exception as e:
                    logger.debug(f"选择器 {i+1} 失败: {str(e)}")
                    continue

            if input_element:
                # 滚动到输入框位置
                try:
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", input_element)
                    time.sleep(1)
                    logger.debug(f"✅ 成功滚动到输入框位置")
                except Exception as e:
                    logger.debug(f"滚动到输入框失败: {str(e)}")

                return input_element
            else:
                logger.error(f"❌ 所有选择器都无法找到角色 {role_name} 的输入框")
                return None

        except Exception as e:
            logger.error(f"❌ 查找角色 {role_name} 输入框时发生异常: {str(e)}")
            return None

    def _click_review_header_to_activate(self):
        """点击"评审角色"或"评审人"文字让填写的人员生效"""
        try:
            logger.info("🖱️ 点击评审表头文字让人员填写生效...")

            # 多种方式定位"评审角色"或"评审人"文字
            header_selectors = [
                # 方法1: 直接通过文本内容定位"评审角色"
                "//th//div[contains(text(), '评审角色')]",

                # 方法2: 直接通过文本内容定位"评审人"
                "//th//div[contains(text(), '评审人')]",

                # 方法3: 通过表头单元格定位
                "//th[contains(@class, 'el-table__cell')]//div[text()='评审角色']",
                "//th[contains(@class, 'el-table__cell')]//div[text()='评审人']",

                # 方法4: 通过cell类定位
                "//div[@class='cell' and text()='评审角色']",
                "//div[@class='cell' and text()='评审人']",

                # 方法5: 更宽泛的文本匹配
                "//*[text()='评审角色' or text()='评审人']",

                # 方法6: 包含评审字样的表头
                "//th[contains(., '评审')]//div[@class='cell']"
            ]

            clicked_successfully = False

            for i, selector in enumerate(header_selectors):
                try:
                    logger.debug(f"尝试表头选择器 {i+1}: {selector}")

                    # 查找元素
                    elements = self.driver.find_elements(By.XPATH, selector)

                    # 过滤出可见的元素
                    visible_elements = []
                    for element in elements:
                        try:
                            if element.is_displayed():
                                visible_elements.append(element)
                        except Exception:
                            continue

                    if visible_elements:
                        # 选择第一个可见元素
                        target_element = visible_elements[0]

                        # 滚动到元素位置
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", target_element)
                        time.sleep(1)

                        # 多种点击方式
                        click_methods = [
                            # 方法1: 直接点击
                            lambda: target_element.click(),
                            # 方法2: JavaScript点击
                            lambda: self.driver.execute_script("arguments[0].click();", target_element),
                            # 方法3: 使用Actions点击
                            lambda: self._action_click_element(target_element)
                        ]

                        for click_method in click_methods:
                            try:
                                click_method()
                                time.sleep(2)  # 等待点击生效
                                logger.info(f"✅ 成功点击表头文字: {target_element.text}")
                                clicked_successfully = True
                                break
                            except Exception as e:
                                logger.debug(f"点击方法失败: {str(e)}")
                                continue

                        if clicked_successfully:
                            break

                except Exception as e:
                    logger.debug(f"表头选择器 {i+1} 失败: {str(e)}")
                    continue

            if not clicked_successfully:
                logger.warning("⚠️ 无法点击评审表头文字，但继续执行...")
                # 作为备选方案，点击页面空白区域
                try:
                    body = self.driver.find_element(By.TAG_NAME, "body")
                    self.driver.execute_script("arguments[0].click();", body)
                    time.sleep(1)
                    logger.info("✅ 备选方案: 点击页面空白区域")
                except Exception:
                    pass

            return clicked_successfully

        except Exception as e:
            logger.warning(f"⚠️ 点击评审表头文字失败: {str(e)}")
            return False

    def _is_input_active(self, input_element):
        """检查输入框是否已激活"""
        try:
            # 检查是否是当前焦点元素
            active_element = self.driver.switch_to.active_element
            if active_element == input_element:
                return True
                
            # 检查是否有active类名或其他激活状态标识
            class_name = input_element.get_attribute('class') or ''
            if 'focus' in class_name or 'active' in class_name:
                return True
                
            # 检查父元素是否有激活状态
            parent = input_element.find_element(By.XPATH, '..')
            parent_class = parent.get_attribute('class') or ''
            if 'focus' in parent_class or 'active' in parent_class:
                return True
                
            return False
            
        except Exception:
            return False
    
    def _action_click_element(self, element):
        """使用ActionChains点击元素"""
        try:
            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(self.driver)
            actions.move_to_element(element).click().perform()
            return True
        except Exception as e:
            logger.debug(f"ActionChains点击失败: {str(e)}")
            return False
    
    def _prepare_for_next_person_input(self, input_element, role_xpath):
        """为下一个人员输入做准备 - Element UI多选框专用"""
        try:
            logger.info("🔄 准备输入下一个人员...")
            
            # 方法1: 重新点击输入框区域
            try:
                # 找到输入框的可点击区域
                input_wrapper = input_element.find_element(By.XPATH, './ancestor::div[contains(@class, "el-select__wrapper")]')
                input_wrapper.click()
                time.sleep(2)
                logger.info("✅ 方法1: 点击输入框包装器成功")
                return True
            except Exception as e:
                logger.debug(f"方法1失败: {str(e)}")
            
            # 方法2: 使用JavaScript模拟点击输入区域
            try:
                self.driver.execute_script("""
                    var inputEl = arguments[0];
                    // 查找el-select包装器
                    var selectWrapper = inputEl.closest('.el-select__wrapper') || inputEl.closest('.el-select');
                    if (selectWrapper) {
                        var rect = selectWrapper.getBoundingClientRect();
                        var clickEvent = new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            clientX: rect.left + rect.width - 50,  // 点击右侧空白处
                            clientY: rect.top + rect.height / 2
                        });
                        selectWrapper.dispatchEvent(clickEvent);
                    }
                """, input_element)
                time.sleep(2)
                logger.info("✅ 方法2: JavaScript点击选择器包装器成功")
                return True
            except Exception as e:
                logger.debug(f"方法2失败: {str(e)}")
            
            # 方法3: 重新定位输入框并点击
            try:
                # 重新查找输入框
                new_input = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, role_xpath))
                )
                new_input.click()
                time.sleep(2)
                logger.info("✅ 方法3: 重新定位输入框成功")
                return True
            except Exception as e:
                logger.debug(f"方法3失败: {str(e)}")
            
            # 方法4: 使用Tab键导航
            try:
                from selenium.webdriver.common.keys import Keys
                input_element.send_keys(Keys.TAB)
                time.sleep(1)
                active_element = self.driver.switch_to.active_element
                active_element.send_keys(Keys.SHIFT + Keys.TAB)  # 回到输入框
                time.sleep(2)
                logger.info("✅ 方法4: 键盘导航成功")
                return True
            except Exception as e:
                logger.debug(f"方法4失败: {str(e)}")
                
            logger.warning("⚠️ 所有准备下一个输入的方法都失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 准备下一个人员输入失败: {str(e)}")
            return False
    
    def _select_person_from_dropdown(self, search_text):
        """从下拉框中选择人员 - 增强版浮动项定位和点击"""
        try:
            logger.info(f"🔍 等待并选择搜索结果: {search_text}")
            
            # 等待下拉框出现
            time.sleep(3)  # 增加等待时间让网页反应
            
            # 方法1: Element UI 特定的下拉选项定位（最可靠）
            try:
                # 查找具有特定ID模式的下拉项（基于HTML结构分析）
                dropdown_selectors = [
                    # Element UI 标准下拉选项
                    "//div[contains(@class, 'el-select-dropdown') and contains(@style, 'z-index')]//li[contains(@class, 'el-select-dropdown__item') and @role='option']",
                    "//ul[contains(@class, 'el-select-dropdown__list')]//li[contains(@class, 'el-select-dropdown__item')]",
                    # 通用下拉项，基于role属性
                    "//li[@role='option' and contains(@id, 'el-id-')]",
                    "//div[@role='option' and contains(@id, 'el-id-')]",
                    # 浮动下拉框（基于z-index样式）
                    "//*[contains(@style, 'z-index') and contains(@style, 'position')]//li[text()]",
                    "//*[contains(@style, 'z-index') and contains(@style, 'position')]//div[text()]"
                ]
                
                for selector in dropdown_selectors:
                    try:
                        options = WebDriverWait(self.driver, 4).until(
                            EC.presence_of_all_elements_located((By.XPATH, selector))
                        )
                        
                        visible_options = [opt for opt in options if opt.is_displayed() and opt.text.strip()]
                        if visible_options:
                            # 选择第一个可见选项
                            target_option = visible_options[0]
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", target_option)
                            time.sleep(1)  # 等待滚动完成
                            self.driver.execute_script("arguments[0].click();", target_option)
                            time.sleep(3)  # 增加等待让网页反应
                            logger.info(f"✅ 方法1成功: 使用选择器 {selector} 选择了 '{target_option.text.strip()}'")
                            return True
                            
                    except Exception as e:
                        logger.debug(f"选择器 {selector} 失败: {str(e)}")
                        continue
                        
            except Exception as e:
                logger.debug(f"方法1整体失败: {str(e)}")
            
            # 方法2: 基于页面特有的浮动层结构定位
            try:
                # 查找浮动层中的选项（基于HTML分析）
                floating_selectors = [
                    # 查找具有高z-index的浮动容器中的选项
                    "//*[contains(@style, 'z-index: 20') or contains(@style, 'z-index: 30') or contains(@style, 'z-index: 40')]//li",
                    "//*[contains(@style, 'z-index: 20') or contains(@style, 'z-index: 30') or contains(@style, 'z-index: 40')]//div[text()]",
                    # El-Popper 组件的选项
                    "//div[contains(@class, 'el-popper')]//li[contains(@class, 'el-select-dropdown__item')]",
                    "//div[contains(@class, 'el-popper')]//li[text()]",
                    # 具有aria-controls属性的相关选项
                    "//*[contains(@aria-controls, 'el-id-')]//li",
                    "//*[contains(@aria-controls, 'el-id-')]//div[text()]"
                ]
                
                for selector in floating_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        visible_elements = [el for el in elements if el.is_displayed() and el.text.strip()]
                        
                        if visible_elements:
                            target_element = visible_elements[0]
                            # 滚动到视口并点击
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", target_element)
                            time.sleep(1)  # 等待滚动完成
                            
                            # 尝试多种点击方式
                            click_methods = [
                                lambda: target_element.click(),
                                lambda: self.driver.execute_script("arguments[0].click();", target_element),
                                lambda: self._simulate_mouse_click(target_element)
                            ]
                            
                            for click_method in click_methods:
                                try:
                                    click_method()
                                    time.sleep(3)  # 增加等待让网页反应
                                    logger.info(f"✅ 方法2成功: 使用选择器 {selector} 选择了 '{target_element.text.strip()}'")
                                    return True
                                except Exception:
                                    continue
                                    
                    except Exception as e:
                        logger.debug(f"浮动选择器 {selector} 失败: {str(e)}")
                        continue
                        
            except Exception as e:
                logger.debug(f"方法2整体失败: {str(e)}")
            
            # 方法3: 智能文本匹配选择
            try:
                # 查找所有可能包含用户信息的元素
                text_selectors = [
                    "//*[contains(text(), '@') or contains(text(), '.com') or contains(text(), '63') or contains(text(), '68')]",
                    "//*[text() and (string-length(text()) > 3) and (string-length(text()) < 50)]",
                    "//li[text()][position() <= 5]",  # 前5个有文本的li元素
                    "//div[text()][position() <= 10][string-length(text()) > 3]"  # 前10个有合适文本的div元素
                ]
                
                for selector in text_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        candidates = []
                        
                        for element in elements:
                            if (element.is_displayed() and 
                                element.text.strip() and 
                                self._is_likely_user_option(element.text.strip())):
                                candidates.append(element)
                        
                        if candidates:
                            # 选择最佳候选项
                            target = candidates[0]
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", target)
                            time.sleep(0.5)
                            self.driver.execute_script("arguments[0].click();", target)
                            time.sleep(2)
                            logger.info(f"✅ 方法3成功: 智能选择了 '{target.text.strip()}'")
                            return True
                            
                    except Exception as e:
                        logger.debug(f"智能选择器 {selector} 失败: {str(e)}")
                        continue
                        
            except Exception as e:
                logger.debug(f"方法3整体失败: {str(e)}")
            
            # 方法4: 键盘导航确认
            try:
                from selenium.webdriver.common.keys import Keys
                
                # 获取当前活动元素
                active_element = self.driver.switch_to.active_element
                
                # 尝试键盘导航
                keyboard_sequences = [
                    [Keys.ARROW_DOWN, Keys.ENTER],
                    [Keys.ENTER],
                    [Keys.TAB],
                    [Keys.ARROW_DOWN, Keys.ARROW_DOWN, Keys.ENTER]
                ]
                
                for sequence in keyboard_sequences:
                    try:
                        for key in sequence:
                            active_element.send_keys(key)
                            time.sleep(0.5)
                        
                        time.sleep(2)
                        logger.info(f"✅ 方法4成功: 键盘序列 {sequence} 确认选择")
                        return True
                        
                    except Exception as e:
                        logger.debug(f"键盘序列 {sequence} 失败: {str(e)}")
                        continue
                        
            except Exception as e:
                logger.debug(f"方法4整体失败: {str(e)}")
            
            # 方法5: JavaScript深度搜索和智能点击
            try:
                js_script = f"""
                var searchText = "{search_text}";
                
                // 查找所有可能的选项元素
                var allElements = document.querySelectorAll('li, div, span, a');
                var candidates = [];
                
                for (var i = 0; i < allElements.length; i++) {{
                    var el = allElements[i];
                    var rect = el.getBoundingClientRect();
                    var text = el.textContent ? el.textContent.trim() : '';
                    
                    // 元素必须可见且有文本
                    if (rect.width > 0 && rect.height > 0 && text) {{
                        var score = 0;
                        
                        // 评分系统判断元素是否为用户选项
                        if (text.includes('@')) score += 10;
                        if (text.includes('.com')) score += 8;
                        if (/^\d+$/.test(text)) score += 6;  // 纯数字（工号）
                        if (text.length > 3 && text.length < 30) score += 4;
                        if (el.tagName === 'LI') score += 5;
                        if (el.className.includes('option') || el.className.includes('item')) score += 8;
                        if (el.id.includes('el-id-')) score += 3;
                        if (rect.height > 20 && rect.height < 60) score += 2;  // 合适的高度
                        
                        // 排除明显不是选项的元素
                        if (text.includes('请输入') || text.includes('选择') || text.includes('请选择')) score = 0;
                        if (text.length > 50) score -= 5;
                        
                        if (score >= 8) {{
                            candidates.push({{element: el, score: score, text: text}});
                        }}
                    }}
                }}
                
                // 按评分排序
                candidates.sort(function(a, b) {{ return b.score - a.score; }});
                
                // 尝试点击最佳候选项
                if (candidates.length > 0) {{
                    var best = candidates[0];
                    try {{
                        best.element.scrollIntoView({{block: 'center'}});
                        setTimeout(function() {{
                            best.element.click();
                        }}, 200);
                        return {{success: true, text: best.text, score: best.score}};
                    }} catch(e) {{
                        return {{success: false, error: e.message}};
                    }}
                }}
                
                return {{success: false, candidates: candidates.length}};
                """
                
                result = self.driver.execute_script(js_script)
                if result.get('success'):
                    time.sleep(2)
                    logger.info(f"✅ 方法5成功: JavaScript智能选择了 '{result.get('text')}' (评分: {result.get('score')})")
                    return True
                else:
                    logger.debug(f"JavaScript搜索结果: {result}")
                    
            except Exception as e:
                logger.debug(f"方法5失败: {str(e)}")
            
            logger.warning(f"⚠️ 所有方法都失败，未能选择 {search_text} 的下拉选项")
            return False
            
        except Exception as e:
            logger.error(f"❌ 选择人员失败: {str(e)}")
            return False
    
    def _is_likely_user_option(self, text):
        """判断文本是否可能是用户选项"""
        try:
            if not text or len(text.strip()) < 2:
                return False
                
            text = text.strip()
            
            # 包含邮箱特征
            if '@' in text and '.com' in text:
                return True
                
            # 纯数字（可能是工号）
            if text.isdigit() and 4 <= len(text) <= 10:
                return True
                
            # 包含字母和数字的组合（可能是用户名）
            if any(c.isalpha() for c in text) and any(c.isdigit() for c in text):
                return True
                
            # 常见的用户名模式
            if '.' in text or '_' in text or '-' in text:
                return True
                
            return False
            
        except Exception:
            return False
    
    def _simulate_mouse_click(self, element):
        """模拟鼠标点击"""
        try:
            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(self.driver)
            actions.move_to_element(element).click().perform()
            return True
        except Exception as e:
            logger.debug(f"鼠标点击模拟失败: {str(e)}")
            return False
    
    def _is_valid_search_text(self, text):
        """检查搜索文本是否有效（只包含字母、数字和常见符号）"""
        try:
            if not text or not isinstance(text, str):
                return False
            
            # 移除空格后检查
            text = text.strip()
            if not text:
                return False
            
            # 检查是否只包含字母、数字和常见符号（如@、.、-、_等）
            import re
            # 允许字母、数字、@、.、-、_等常见符号
            valid_pattern = r'^[a-zA-Z0-9@.\-_]+$'
            
            if re.match(valid_pattern, text):
                logger.info(f"✅ 有效的搜索文本: {text}")
                return True
            else:
                logger.warning(f"⚠️ 无效的搜索文本（包含中文或其他字符）: {text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 检查搜索文本失败: {str(e)}")
            return False
    
    def _fill_deadline(self):
        """填写截止时间 - 基于新的HTML结构"""
        try:
            logger.info("🕒 填写截止时间...")

            # 计算一周后的时间作为截止时间
            from datetime import datetime, timedelta
            current_time = datetime.now()
            deadline_time = current_time + timedelta(weeks=1)
            # 格式化为字符串，格式：YYYY-MM-DD
            deadline = deadline_time.strftime("%Y-%m-%d")

            logger.info(f"📅 设置截止时间为: {deadline}")

            # 基于新HTML结构的时间输入框选择器
            try:
                time_input_selectors = [
                    "//input[@id='el-id-9858-114']",  # 基于HTML中的具体ID
                    "//label[contains(text(), '截止日期')]/following-sibling::div//input",
                    "//div[contains(@class, 'el-date-editor')]//input[@class='el-input__inner']",
                    "//input[contains(@aria-haspopup, 'dialog') and contains(@role, 'combobox')]",
                    "//input[contains(@class, 'el-input__inner') and contains(@aria-controls, 'el-id-9858-96')]"
                ]

                time_input = None
                for selector in time_input_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        for element in elements:
                            if element.is_displayed():
                                # 检查是否是截止时间输入框
                                try:
                                    parent_text = element.find_element(By.XPATH, "./ancestor::div[contains(@class, 'el-form-item')]").text
                                    if "截止" in parent_text or "日期" in parent_text:
                                        time_input = element
                                        logger.info(f"✅ 找到截止时间输入框，选择器: {selector}")
                                        break
                                except:
                                    # 如果无法获取父元素文本，直接使用第一个可见元素
                                    time_input = element
                                    logger.info(f"✅ 找到时间输入框，选择器: {selector}")
                                    break
                        if time_input:
                            break
                    except Exception as e:
                        logger.debug(f"选择器 {selector} 失败: {str(e)}")
                        continue

                if not time_input:
                    logger.error("❌ 未找到截止时间输入框")
                    return False

                # 滚动到输入框
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", time_input)
                time.sleep(2)

                # 点击时间输入框
                try:
                    time_input.click()
                    time.sleep(2)
                    logger.info("✅ 成功点击时间输入框")
                except Exception as e:
                    # 尝试JavaScript点击
                    self.driver.execute_script("arguments[0].click();", time_input)
                    time.sleep(2)
                    logger.info("✅ 使用JavaScript点击时间输入框")

                # 清空并填写时间
                try:
                    time_input.clear()
                    time.sleep(1)
                    time_input.send_keys(deadline)
                    time.sleep(3)  # 等待时间输入生效

                    # 按回车键确认输入
                    from selenium.webdriver.common.keys import Keys
                    time_input.send_keys(Keys.ENTER)
                    time.sleep(2)

                    logger.info(f"✅ 成功填写截止时间: {deadline}")
                except Exception as e:
                    logger.error(f"❌ 填写时间失败: {str(e)}")
                    return False

                # 时间填写完成
                logger.info("✅ 截止时间填写完成")
                return True

            except Exception as e:
                logger.error(f"❌ 填写截止时间失败: {str(e)}")
                return False

        except Exception as e:
            logger.error(f"❌ 填写截止时间整体失败: {str(e)}")
            return False
    
    def _submit_form(self):
        """点击提交按钮 - 基于新的HTML结构"""
        try:
            logger.info("📤 提交表单...")

            # 基于commit.html的精确提交按钮选择器
            submit_selectors = [
                # 最精确的选择器 - 基于commit.html中的ID
                "//button[@id='submitBtn']",
                "#submitBtn",

                # 基于新的xpath路径 (div[2]而不是div[3])
                "//*[@id='app']/div/div/div[2]//button[@id='submitBtn']",
                "//*[@id='app']/div/div/div[2]/div/button[@id='submitBtn']",
                # 兼容旧路径
                "//*[@id='app']/div/div/div[3]//button[@id='submitBtn']",
                "//*[@id='app']/div/div/div[3]/div/button[@id='submitBtn']",

                # 基于HTML结构的选择器
                "//div[contains(@class, 'btn-content') and contains(@class, 'detail-card-footer')]//button[@id='submitBtn']",
                "//div[contains(@class, 'btn-content')]//button[contains(@class, 'el-button--primary') and not(contains(@class, 'is-plain'))]",

                # 基于按钮文本的选择器
                "//button[contains(@class, 'el-button--primary') and .//span[text()='提交']]",
                "//button[@type='button' and contains(@class, 'el-button--primary')]//span[text()='提交']/parent::button",

                # 基于页面结构的备用选择器 (支持新旧路径)
                "//*[@id='app']/div/div/div[2]//button[contains(@class, 'el-button--primary') and contains(., '提交')]",
                "//*[@id='app']/div/div/div[3]//button[contains(@class, 'el-button--primary') and contains(., '提交')]",
                "//div[contains(@class, 'detail-card-footer')]//button[contains(@class, 'el-button--primary')][2]",

                # 通用备用选择器
                "//button[contains(@class, 'el-button--primary') and not(contains(@class, 'is-plain'))]",
            ]

            submit_element = None

            # 尝试每个选择器
            for selector in submit_selectors:
                try:
                    # 根据选择器类型使用不同的定位方法
                    if selector.startswith("#"):
                        # CSS ID选择器
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    elif selector.startswith("//") or selector.startswith("//*"):
                        # XPath选择器
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        # 默认当作XPath处理
                        elements = self.driver.find_elements(By.XPATH, selector)

                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            # 对于ID为submitBtn的按钮，直接使用
                            if selector in ["//button[@id='submitBtn']", "#submitBtn"]:
                                submit_element = element
                                logger.info(f"✅ 找到提交按钮(ID=submitBtn)，使用选择器: {selector}")
                                break

                            # 进一步验证是否是提交按钮
                            element_text = element.text.strip()
                            if any(keyword in element_text for keyword in ['提交', 'Submit']):
                                submit_element = element
                                logger.info(f"✅ 找到提交按钮，使用选择器: {selector}, 按钮文本: {element_text}")
                                break
                            elif not element_text:  # 如果按钮没有文本，检查子元素
                                try:
                                    child_text = element.find_element(By.XPATH, ".//span").text.strip()
                                    if any(keyword in child_text for keyword in ['提交', 'Submit']):
                                        submit_element = element
                                        logger.info(f"✅ 找到提交按钮，使用选择器: {selector}, 子元素文本: {child_text}")
                                        break
                                except:
                                    pass

                    if submit_element:
                        break

                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {str(e)}")
                    continue
            
            if not submit_element:
                logger.error("❌ 未找到提交按钮")
                return False
            
            # 滚动到提交按钮
            try:
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", submit_element)
                time.sleep(2)
                logger.info("✅ 滚动到提交按钮")
            except Exception as e:
                logger.debug(f"滚动失败: {str(e)}")
            
            # 多种点击方式
            click_methods = [
                # 方法1: 普通点击
                lambda: submit_element.click(),
                # 方法2: JavaScript点击
                lambda: self.driver.execute_script("arguments[0].click();", submit_element),
                # 方法3: ActionChains点击
                lambda: self._action_click_element(submit_element),
                # 方法4: JavaScript强制点击
                lambda: self.driver.execute_script("""
                    var element = arguments[0];
                    var event = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });
                    element.dispatchEvent(event);
                """, submit_element)
            ]
            
            # 尝试点击
            clicked_successfully = False
            for i, click_method in enumerate(click_methods, 1):
                try:
                    click_method()
                    time.sleep(3)  # 等待提交处理
                    logger.info(f"✅ 方法{i}成功点击提交按钮")
                    clicked_successfully = True
                    break
                except Exception as e:
                    logger.debug(f"点击方法{i}失败: {str(e)}")
                    continue
            
            if not clicked_successfully:
                logger.error("❌ 所有点击方法都失败")
                return False
            
            # 等待提交处理完成
            time.sleep(5)  # 提交后等待5秒
            logger.info("✅ 表单提交完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 提交表单整体失败: {str(e)}")
            return False

    def _handle_post_submit_modal(self, filename=None):
        """处理提交后的模态框 - PDF上传和继续提交"""
        try:
            logger.info("🔍 检查提交后是否出现模态框...")

            # 等待模态框出现 (支持新旧路径)
            modal_xpaths = [
                "//*[@id='app']/div/div/div[2]/div[4]/div/div/div",  # 新路径
                "//*[@id='app']/div/div/div[3]/div[4]/div/div/div"   # 旧路径
            ]

            modal_element = None
            for modal_xpath in modal_xpaths:
                try:
                    modal_element = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, modal_xpath))
                    )
                    logger.info(f"✅ 检测到提交后的模态框，使用路径: {modal_xpath}")
                    break
                except TimeoutException:
                    continue

            if not modal_element:
                logger.info("ℹ️ 未检测到模态框，可能直接提交成功")
                return True

            # 等待模态框完全加载
            time.sleep(3)

            # 直接上传PDF文件（无头模式兼容）
            if not self._upload_pdf_in_modal(filename):
                logger.error("❌ 在模态框中上传PDF失败")
                return False

            # 点击"继续提交"按钮
            if not self._click_continue_submit_button():
                logger.error("❌ 点击继续提交按钮失败")
                return False

            logger.info("✅ 模态框处理完成")
            return True

        except Exception as e:
            logger.error(f"❌ 处理提交后模态框失败: {str(e)}")
            return False

    def _click_reupload_button(self):
        """点击模态框中的重新上传按钮 - 已弃用，无头模式不兼容"""
        logger.warning("⚠️ _click_reupload_button方法已弃用，无头模式下不使用")
        logger.info("ℹ️ 现在直接向文件输入框发送文件路径，无需点击重新上传按钮")
        return True  # 返回True以保持兼容性

    def _upload_pdf_in_modal(self, filename=None):
        """在模态框中上传PDF文件 - 无头模式兼容版本"""
        try:
            logger.info("📋 在模态框中上传PDF文件（无头模式兼容）...")

            # 获取要上传的PDF文件路径
            pdf_file_path = self._get_pdf_file_path(filename)
            if not pdf_file_path:
                logger.error("❌ 未找到要上传的PDF文件")
                return False

            logger.info(f"🔍 准备上传文件: {pdf_file_path}")

            # 查找模态框内的文件输入框（精确定位到模态框内部）
            file_input_selectors = [
                # 基于aftercommit.html的精确结构 - 确保在模态框内
                "//div[contains(@class, 'el-dialog')]//div[@class='el-upload el-upload--text']//input[@class='el-upload__input']",
                "//div[contains(@class, 'el-dialog')]//input[@class='el-upload__input'][@type='file']",
                "//div[contains(@class, 'el-dialog')]//input[@type='file'][@name='file']",

                # 基于模态框xpath的精确定位 (支持新旧路径)
                "//*[@id='app']/div/div/div[2]/div[4]/div/div/div//input[@type='file']",  # 新路径
                "//*[@id='app']/div/div/div[2]/div[4]//input[@class='el-upload__input']",
                "//*[@id='app']/div/div/div[3]/div[4]/div/div/div//input[@type='file']",  # 旧路径
                "//*[@id='app']/div/div/div[3]/div[4]//input[@class='el-upload__input']",

                # 基于模态框内容的定位
                "//div[contains(@class, 'el-dialog-body')]//input[@type='file']",
                "//div[contains(@class, 'el-dialog')]//div[contains(@class, 'el-upload')]//input[@type='file']",

                # 通过父元素文本定位（包含"重新上传"的区域）
                "//div[contains(., '重新上传')]//input[@type='file']",
                "//button[contains(., '重新上传')]/following-sibling::input[@type='file']",
                "//button[contains(., '重新上传')]/..//input[@type='file']",
            ]

            file_input = None
            for selector in file_input_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        # 检查元素是否存在且可用（即使不可见）
                        if element and element.get_attribute('type') == 'file':
                            # 验证这个输入框是否在模态框内
                            if self._is_input_in_modal(element):
                                file_input = element
                                logger.info(f"✅ 找到模态框内的文件输入框，使用选择器: {selector}")
                                break
                            else:
                                logger.debug(f"跳过非模态框内的文件输入框: {selector}")
                    if file_input:
                        break
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {str(e)}")
                    continue

            if not file_input:
                logger.error("❌ 未找到文件输入框")
                # 尝试JavaScript方法查找
                return self._upload_pdf_with_javascript(pdf_file_path)

            # 直接向文件输入框发送文件路径（无头模式兼容）
            try:
                # 确保元素可交互
                self.driver.execute_script("arguments[0].style.display = 'block';", file_input)
                self.driver.execute_script("arguments[0].style.visibility = 'visible';", file_input)

                # 发送文件路径
                file_input.send_keys(pdf_file_path)
                logger.info(f"✅ 成功上传PDF文件: {pdf_file_path}")

                # 等待文件上传完成
                time.sleep(5)

                # 验证文件是否上传成功
                if self._verify_file_upload_success():
                    logger.info("✅ 文件上传验证成功")
                    return True
                else:
                    logger.warning("⚠️ 文件上传验证失败，但继续执行")
                    return True

            except Exception as e:
                logger.error(f"❌ 直接上传PDF文件失败: {str(e)}")
                # 尝试JavaScript方法
                return self._upload_pdf_with_javascript(pdf_file_path)

        except Exception as e:
            logger.error(f"❌ 在模态框中上传PDF整体失败: {str(e)}")
            return False

    def _upload_pdf_with_javascript(self, pdf_file_path):
        """使用JavaScript方法上传PDF文件"""
        try:
            logger.info("🔧 尝试使用JavaScript方法上传PDF...")

            # JavaScript代码来处理模态框内的文件上传
            js_script = """
            // 精确查找模态框内的文件输入框
            var targetInput = null;

            // 方法1: 通过模态框类名查找
            var dialogs = document.querySelectorAll('.el-dialog, [role="dialog"]');
            for (var d = 0; d < dialogs.length; d++) {
                var dialog = dialogs[d];
                // 检查是否是可见的模态框
                if (dialog.offsetParent !== null) {
                    var inputs = dialog.querySelectorAll('input[type="file"]');
                    if (inputs.length > 0) {
                        targetInput = inputs[0];
                        break;
                    }
                }
            }

            // 方法2: 通过"重新上传"文本查找
            if (!targetInput) {
                var buttons = document.querySelectorAll('button');
                for (var b = 0; b < buttons.length; b++) {
                    var button = buttons[b];
                    if (button.textContent && button.textContent.includes('重新上传')) {
                        // 查找同一容器内的文件输入框
                        var container = button.closest('div');
                        while (container) {
                            var fileInput = container.querySelector('input[type="file"]');
                            if (fileInput) {
                                targetInput = fileInput;
                                break;
                            }
                            container = container.parentElement;
                        }
                        if (targetInput) break;
                    }
                }
            }

            // 方法3: 通过特定的类名组合查找
            if (!targetInput) {
                var uploadDivs = document.querySelectorAll('.el-upload.el-upload--text');
                for (var u = 0; u < uploadDivs.length; u++) {
                    var uploadDiv = uploadDivs[u];
                    // 检查是否在可见的模态框内
                    var dialog = uploadDiv.closest('.el-dialog');
                    if (dialog && dialog.offsetParent !== null) {
                        var input = uploadDiv.querySelector('input[type="file"]');
                        if (input) {
                            targetInput = input;
                            break;
                        }
                    }
                }
            }

            if (targetInput) {
                // 使元素可见和可交互
                targetInput.style.display = 'block';
                targetInput.style.visibility = 'visible';
                targetInput.style.opacity = '1';
                targetInput.style.position = 'static';
                targetInput.style.width = 'auto';
                targetInput.style.height = 'auto';

                return {success: true, element: targetInput, method: 'found'};
            }

            return {success: false, message: '未找到模态框内的文件输入框'};
            """

            result = self.driver.execute_script(js_script)

            if result.get('success'):
                # 找到了文件输入框，尝试上传
                try:
                    # 重新查找元素并上传
                    file_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file']")
                    if file_inputs:
                        target_input = file_inputs[-1]  # 使用最后一个
                        target_input.send_keys(pdf_file_path)
                        logger.info("✅ JavaScript方法上传成功")
                        time.sleep(5)
                        return True
                except Exception as e:
                    logger.error(f"❌ JavaScript方法上传失败: {str(e)}")

            logger.error("❌ JavaScript方法也失败了")
            return False

        except Exception as e:
            logger.error(f"❌ JavaScript上传方法失败: {str(e)}")
            return False

    def _verify_file_upload_success(self):
        """验证文件上传是否成功"""
        try:
            logger.info("🔍 验证文件上传状态...")

            # 等待一段时间让上传完成
            time.sleep(2)

            # 检查是否有上传成功的指示器
            success_indicators = [
                # 查找上传成功的文本
                "//*[contains(text(), '上传成功')]",
                "//*[contains(text(), '上传完成')]",
                "//*[contains(text(), '100%')]",

                # 查找文件名显示
                "//div[contains(@class, 'el-upload-list')]//div[contains(@class, 'el-upload-list__item')]",
                "//span[contains(@class, 'el-upload-list__item-name')]",

                # 查找进度条完成状态
                "//div[contains(@class, 'el-progress') and contains(@class, 'is-success')]",

                # 查找删除按钮（通常上传成功后会出现）
                "//i[contains(@class, 'el-icon-delete')]",
            ]

            for indicator in success_indicators:
                try:
                    elements = self.driver.find_elements(By.XPATH, indicator)
                    if elements and any(el.is_displayed() for el in elements):
                        logger.info(f"✅ 找到上传成功指示器: {indicator}")
                        return True
                except:
                    continue

            # 如果没有找到明确的成功指示器，检查是否有错误信息
            error_indicators = [
                "//*[contains(text(), '上传失败')]",
                "//*[contains(text(), '错误')]",
                "//*[contains(text(), '失败')]",
                "//div[contains(@class, 'el-message--error')]",
            ]

            for indicator in error_indicators:
                try:
                    elements = self.driver.find_elements(By.XPATH, indicator)
                    if elements and any(el.is_displayed() for el in elements):
                        logger.warning(f"⚠️ 发现上传错误指示器: {indicator}")
                        return False
                except:
                    continue

            # 如果既没有成功也没有失败指示器，假设成功
            logger.info("ℹ️ 未找到明确的上传状态指示器，假设上传成功")
            return True

        except Exception as e:
            logger.warning(f"⚠️ 验证文件上传状态失败: {str(e)}")
            return True  # 验证失败时假设成功，避免阻塞流程

    def _is_input_in_modal(self, input_element):
        """验证文件输入框是否在模态框内"""
        try:
            # 方法1: 检查是否在el-dialog内
            try:
                modal_parent = input_element.find_element(By.XPATH, "./ancestor::div[contains(@class, 'el-dialog')]")
                if modal_parent and modal_parent.is_displayed():
                    logger.debug("✅ 输入框在el-dialog内")
                    return True
            except:
                pass

            # 方法2: 检查是否在指定的模态框xpath内 (支持新旧路径)
            try:
                modal_xpaths = [
                    "//*[@id='app']/div/div/div[2]/div[4]/div/div/div",  # 新路径
                    "//*[@id='app']/div/div/div[3]/div[4]/div/div/div"   # 旧路径
                ]

                for modal_xpath in modal_xpaths:
                    try:
                        modal_element = self.driver.find_element(By.XPATH, modal_xpath)
                        if modal_element and modal_element.is_displayed():
                            # 检查输入框是否是模态框的子元素
                            js_script = """
                            var modal = arguments[0];
                            var input = arguments[1];
                            return modal.contains(input);
                            """
                            is_contained = self.driver.execute_script(js_script, modal_element, input_element)
                            if is_contained:
                                logger.debug(f"✅ 输入框在指定模态框内: {modal_xpath}")
                                return True
                    except:
                        continue
            except:
                pass

            # 方法3: 检查父元素是否包含"重新上传"相关内容
            try:
                parent_elements = input_element.find_elements(By.XPATH, "./ancestor::div[contains(., '重新上传') or contains(., '预览PDF')]")
                if parent_elements:
                    logger.debug("✅ 输入框在包含'重新上传'的区域内")
                    return True
            except:
                pass

            # 方法4: 检查是否在dialog body内
            try:
                dialog_body = input_element.find_element(By.XPATH, "./ancestor::div[contains(@class, 'el-dialog__body')]")
                if dialog_body and dialog_body.is_displayed():
                    logger.debug("✅ 输入框在dialog body内")
                    return True
            except:
                pass

            logger.debug("❌ 输入框不在模态框内")
            return False

        except Exception as e:
            logger.debug(f"⚠️ 验证输入框位置失败: {str(e)}")
            return False  # 验证失败时返回False，避免误用

    def _get_pdf_file_path(self, filename=None):
        """获取要上传的PDF文件路径"""
        try:
            logger.info("🔍 查找要上传的PDF文件...")

            # 查找Final_Approval_Documents文件夹中的PDF文件
            documents_folder = Path("Final_Approval_Documents")
            if not documents_folder.exists():
                logger.error("❌ Final_Approval_Documents文件夹不存在")
                return None

            # 查找PDF文件
            pdf_files = list(documents_folder.glob("*.pdf"))
            if not pdf_files:
                logger.error("❌ Final_Approval_Documents文件夹中没有PDF文件")
                return None

            # 如果提供了文件名，尝试匹配
            if filename:
                logger.info(f"🔍 尝试匹配文件名: {filename}")
                # 移除扩展名进行匹配
                base_filename = Path(filename).stem
                for pdf_file in pdf_files:
                    if base_filename in pdf_file.stem:
                        logger.info(f"✅ 找到匹配的PDF文件: {pdf_file.name}")
                        return str(pdf_file.resolve())

            # 如果没有匹配到或没有提供文件名，使用最新的PDF文件
            latest_pdf = max(pdf_files, key=lambda f: f.stat().st_mtime)
            logger.info(f"✅ 使用最新的PDF文件: {latest_pdf.name}")
            return str(latest_pdf.resolve())

        except Exception as e:
            logger.error(f"❌ 获取PDF文件路径失败: {str(e)}")
            return None

    def _click_continue_submit_button(self):
        """点击模态框中的继续提交按钮"""
        try:
            logger.info("🖱️ 点击继续提交按钮...")

            # 等待上传完成
            time.sleep(3)

            # 基于aftercommit.html的结构定位继续提交按钮
            continue_submit_selectors = [
                # 基于HTML结构的精确选择器
                "//footer[@class='el-dialog__footer']//button[contains(@class, 'el-button--primary') and .//span[text()='继续提交']]",
                "//div[contains(@style, 'text-align: center')]//button[contains(@class, 'el-button--primary') and .//span[text()='继续提交']]",
                "//button[.//span[text()='继续提交']]",

                # 备用选择器
                "//button[contains(text(), '继续提交')]",
                "//footer//button[contains(@class, 'el-button--primary')]",
                "//*[contains(text(), '继续提交')]",
            ]

            continue_button = None
            for selector in continue_submit_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            continue_button = element
                            logger.info(f"✅ 找到继续提交按钮，使用选择器: {selector}")
                            break
                    if continue_button:
                        break
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {str(e)}")
                    continue

            if not continue_button:
                logger.error("❌ 未找到继续提交按钮")
                return False

            # 滚动到按钮位置
            try:
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", continue_button)
                time.sleep(1)
            except Exception as e:
                logger.debug(f"滚动失败: {str(e)}")

            # 点击继续提交按钮
            click_methods = [
                # 方法1: 普通点击
                lambda: continue_button.click(),
                # 方法2: JavaScript点击
                lambda: self.driver.execute_script("arguments[0].click();", continue_button),
                # 方法3: ActionChains点击
                lambda: self._action_click_element(continue_button),
                # 方法4: JavaScript强制点击
                lambda: self.driver.execute_script("""
                    var element = arguments[0];
                    var event = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });
                    element.dispatchEvent(event);
                """, continue_button)
            ]

            # 尝试点击
            clicked_successfully = False
            for i, click_method in enumerate(click_methods, 1):
                try:
                    click_method()
                    time.sleep(3)  # 等待提交处理
                    logger.info(f"✅ 方法{i}成功点击继续提交按钮")
                    clicked_successfully = True
                    break
                except Exception as e:
                    logger.debug(f"点击方法{i}失败: {str(e)}")
                    continue

            if not clicked_successfully:
                logger.error("❌ 所有点击方法都失败")
                return False

            # 等待最终提交完成
            time.sleep(5)
            logger.info("✅ 继续提交完成")
            return True

        except Exception as e:
            logger.error(f"❌ 点击继续提交按钮整体失败: {str(e)}")
            return False
