# 评审人方案选择问题修复总结

## 问题描述

1. **PPL点击评审人方案框时，点击到上面的另一个非评审人方案选项框里去了**
2. **FN也是找不到评审人方案的输入框，点击到上面的另一个输入框里去了**
3. **PPL填数据管理员时一开始已经填进去了，但后面又点击了一下数据管理员的框，第一个填的也没有了**

## 根本原因分析

通过分析 `fn.html` 和 `ppl.html` 文件，发现：

1. **元素ID不同**：
   - FN的评审人方案输入框ID: `el-id-8960-115`
   - PPL的评审人方案输入框ID: `el-id-9125-115`
   - 原代码使用通用选择器，容易选择到错误的元素

2. **定位不够精确**：
   - 原代码使用的xpath选择器过于宽泛
   - 没有限制在评审人信息区域内查找
   - 容易匹配到页面上其他的输入框

3. **多选框处理不当**：
   - 数据管理员输入框是多选框
   - 重复点击时会清空已有内容
   - 没有检查是否已存在相同人员

## 修复方案

### 1. 精确的元素定位

基于HTML文件分析，更新了评审人方案配置：

```python
"FN": {
    "scheme_xpath": [
        "//input[@id='el-id-8960-115']",  # FN的精确ID
        "//label[@for='el-id-8960-115']/following-sibling::div//input[@class='el-select__input']",
        "//*[@id='el-collapse-content-97']//label[contains(text(), '评审人方案')]/following-sibling::div//input[@class='el-select__input']",
        "//*[@id='el-collapse-content-97']//input[@class='el-select__input' and @role='combobox']"
    ]
},
"PPL": {
    "scheme_xpath": [
        "//input[@id='el-id-9125-115']",  # PPL的精确ID
        "//label[@for='el-id-9125-115']/following-sibling::div//input[@class='el-select__input']",
        "//*[@id='el-collapse-content-97']//label[contains(text(), '评审人方案')]/following-sibling::div//input[@class='el-select__input']",
        "//*[@id='el-collapse-content-97']//input[@class='el-select__input' and @role='combobox']"
    ]
}
```

### 2. 新增精确定位方法

添加了 `_find_reviewer_scheme_input_precisely()` 方法：

- 根据文件类型使用对应的精确ID
- 多种定位策略的fallback机制
- 限制在评审人信息区域内查找

### 3. 智能内容保护

添加了 `_should_preserve_existing_content()` 方法：

- 检测多选框类型
- 识别数据管理员角色
- 避免清空已有内容
- 防止重复添加相同人员

### 4. 更严格的方案匹配

改进了 `_scheme_matches()` 方法：

- 要求100%关键词匹配
- 检查关键词数量一致性
- 处理括号和特殊字符
- 精确匹配优先

### 5. 容器路径更新

基于 `xpath.txt` 更新了页面容器路径：

```python
# 统一使用精确的容器路径
"//*[@id='app']/div/div/div[2]/div/div[2]/div[2]"
```

## 修复后的流程

### 评审人方案选择流程：

1. **精确定位**: 使用文件类型对应的精确ID定位输入框
2. **区域限制**: 限制在 `el-collapse-content-97` 评审人信息区域内查找
3. **多重验证**: 通过ID、label、文本等多种方式验证
4. **严格匹配**: 使用100%关键词匹配确保选择正确方案

### 数据管理员填写流程：

1. **内容检查**: 检查输入框是否已有内容
2. **类型识别**: 识别多选框类型
3. **智能保护**: 对数据管理员角色保护已有内容
4. **重复检测**: 避免添加重复人员

## 测试验证

创建了 `test_fixes.py` 测试脚本，验证：

- ✅ FN配置包含正确ID (el-id-8960-115)
- ✅ PPL配置包含正确ID (el-id-9125-115)
- ✅ 方案匹配逻辑正确
- ✅ 精确定位方法可用

## 预期效果

1. **PPL和FN能够准确点击评审人方案输入框**
2. **不会误点击其他输入框**
3. **数据管理员内容不会被意外清空**
4. **方案选择更加精确可靠**

## 关键改进点

1. **从通用选择器改为精确ID定位**
2. **增加区域限制避免误选**
3. **添加内容保护机制**
4. **改进方案匹配算法**
5. **增强错误处理和日志**

这些修复应该能够解决您提到的所有问题，使PPL和FN的评审人方案选择更加准确可靠。
