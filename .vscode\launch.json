{"version": "0.2.0", "configurations": [{"name": "Python: Current File (Debug Console)", "type": "debugpy", "request": "launch", "program": "${file}", "console": "internalConsole", "args": ["-f", "AMOUNT>=0,02"], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true, "subProcess": true, "logToFile": true, "gevent": false, "internalConsoleOptions": "openOnSessionStart", "presentation": {"hidden": false, "group": "Python", "order": 1}, "envFile": "${workspaceFolder}/.env", "autoReload": {"enable": true}, "purpose": ["debug-in-terminal", "debug-test"]}]}